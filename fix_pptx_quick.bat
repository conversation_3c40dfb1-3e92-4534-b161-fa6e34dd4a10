@echo off
chcp 65001 >nul
echo 🔧 开始修复PPTX文件上传问题...

REM 检测Docker部署
docker ps | findstr ragflow >nul 2>&1
if %errorlevel% == 0 (
    echo 📦 检测到Docker部署
    
    REM 获取容器名称
    for /f "tokens=*" %%i in ('docker ps ^| findstr ragflow') do (
        for %%j in (%%i) do set CONTAINER_NAME=%%j
    )
    echo 容器名称: %CONTAINER_NAME%
    
    REM 在容器中安装
    echo 📥 在Docker容器中安装aspose-slides-python...
    docker exec -it %CONTAINER_NAME% pip install aspose-slides-python
    
    if %errorlevel% == 0 (
        echo ✅ 安装成功
        
        REM 验证安装
        echo 🔍 验证安装...
        docker exec -it %CONTAINER_NAME% python -c "import aspose.slides; print('Aspose安装验证成功')"
        
        if %errorlevel% == 0 (
            echo ✅ 验证成功
            
            REM 重启容器
            echo 🔄 重启RAGFlow容器...
            docker restart %CONTAINER_NAME%
            
            echo ✅ 修复完成！请等待容器重启后重试上传PPTX文件
        ) else (
            echo ❌ 验证失败，请检查安装
        )
    ) else (
        echo ❌ 安装失败，尝试其他方式...
        docker exec -it %CONTAINER_NAME% pip install aspose.slides
    )
    
) else (
    echo 🐍 尝试直接Python安装...
    
    REM 直接安装
    pip install aspose-slides-python
    
    if %errorlevel% == 0 (
        echo ✅ 安装成功
        
        REM 验证安装
        echo 🔍 验证安装...
        python -c "import aspose.slides; print('Aspose安装验证成功')"
        
        if %errorlevel% == 0 (
            echo ✅ 验证成功，请重启RAGFlow服务
        ) else (
            echo ❌ 验证失败
        )
    ) else (
        echo ❌ 安装失败，请检查Python环境
    )
)

echo.
echo 📋 如果问题仍然存在，请：
echo 1. 检查RAGFlow日志
echo 2. 确认Python环境正确  
echo 3. 尝试手动安装: pip install aspose-slides-python
echo 4. 重启RAGFlow服务

pause
