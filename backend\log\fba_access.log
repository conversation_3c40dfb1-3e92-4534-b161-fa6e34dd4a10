2025-08-25 07:57:20.602 | INFO     | 38c64d53b4144ccd9e44ad637b55063a | JWT标准验证成功，获取UUID: 88b883c5-f919-4d9f-964d-77590b057a43
2025-08-25 07:57:20.708 | INFO     | 38c64d53b4144ccd9e44ad637b55063a | 127.0.0.1       | GET      | 500    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 121.783ms
2025-08-25 07:57:22.106 | INFO     | d28dcea8805a4b538463f2113e6502f5 | JWT标准验证成功，获取UUID: 88b883c5-f919-4d9f-964d-77590b057a43
2025-08-25 07:57:22.114 | INFO     | d28dcea8805a4b538463f2113e6502f5 | 127.0.0.1       | GET      | 500    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 9.029ms
2025-08-25 08:04:49.424 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-25 08:05:52.265 | INFO     | 3fb3a98f62b3450b9e6c531ac2f89219 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:05:52.315 | INFO     | 3fb3a98f62b3450b9e6c531ac2f89219 | 成功认证Java用户: admin
2025-08-25 08:05:52.353 | INFO     | 3fb3a98f62b3450b9e6c531ac2f89219 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:05:52.354 | INFO     | 3fb3a98f62b3450b9e6c531ac2f89219 | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-25 08:05:52.430 | INFO     | 3fb3a98f62b3450b9e6c531ac2f89219 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:05:52.439 | INFO     | 3fb3a98f62b3450b9e6c531ac2f89219 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 206.853ms
2025-08-25 08:05:52.463 | INFO     | e06e330233254d868be14a2aeaf4f8a7 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:05:52.466 | INFO     | e06e330233254d868be14a2aeaf4f8a7 | 成功认证Java用户: admin
2025-08-25 08:05:52.491 | INFO     | e06e330233254d868be14a2aeaf4f8a7 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:05:52.492 | INFO     | e06e330233254d868be14a2aeaf4f8a7 | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-25 08:05:52.520 | INFO     | e06e330233254d868be14a2aeaf4f8a7 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:05:52.523 | INFO     | e06e330233254d868be14a2aeaf4f8a7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 59.616ms
2025-08-25 08:05:53.839 | INFO     | 73538cf4a42a49f2ba1756c1750ceadb | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:05:53.841 | INFO     | 73538cf4a42a49f2ba1756c1750ceadb | 成功认证Java用户: admin
2025-08-25 08:05:53.845 | INFO     | 73538cf4a42a49f2ba1756c1750ceadb | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:05:53.846 | INFO     | 73538cf4a42a49f2ba1756c1750ceadb | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-25 08:05:53.862 | INFO     | 73538cf4a42a49f2ba1756c1750ceadb | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:05:53.864 | INFO     | 73538cf4a42a49f2ba1756c1750ceadb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 23.665ms
2025-08-25 08:05:53.879 | INFO     | 31def655924c4e9e956bb2a7b65e9aa1 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:05:53.881 | INFO     | 31def655924c4e9e956bb2a7b65e9aa1 | 成功认证Java用户: admin
2025-08-25 08:05:53.885 | INFO     | 31def655924c4e9e956bb2a7b65e9aa1 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:05:53.886 | INFO     | 31def655924c4e9e956bb2a7b65e9aa1 | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-25 08:05:53.904 | INFO     | 31def655924c4e9e956bb2a7b65e9aa1 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:05:53.906 | INFO     | 31def655924c4e9e956bb2a7b65e9aa1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 27.197ms
2025-08-25 08:05:57.781 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:05:57.782 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | 成功认证Java用户: admin
2025-08-25 08:05:57.786 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:05:57.786 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | 权限检查通过: user_id=1, permission=knowledge:base:view
2025-08-25 08:05:57.894 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:05:57.895 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | 文档预览调试 - doc_id: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:05:57.896 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | doc_name: '新文件 8.txt', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-25 08:05:57.896 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-25 08:05:57.896 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | 文件头检测失败，根据文件名识别类型: text (基于文件名: '新文件 8.txt')
2025-08-25 08:05:57.897 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | 最终识别的文档类型: text
2025-08-25 08:05:57.897 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | 成功使用 utf-8 编码解析文本内容
2025-08-25 08:05:57.898 | INFO     | b55f7e30f4324f1b93ef0e4263dcbec7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/preview/doc_name=%E6%96%B0%E6%96%87%E4%BB%B6+8.txt | 117.558ms
2025-08-25 08:06:01.230 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:06:01.232 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | 成功认证Java用户: admin
2025-08-25 08:06:01.236 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:06:01.237 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | 权限检查通过: user_id=1, permission=knowledge:base:view
2025-08-25 08:06:01.265 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbbf41967e3311f09a8488f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:06:01.266 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | 文档预览调试 - doc_id: fbbf41967e3311f09a8488f4da8e1b91
2025-08-25 08:06:01.266 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | doc_name: '新文件 8 (2).txt', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-25 08:06:01.266 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-25 08:06:01.267 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | 文件头检测失败，根据文件名识别类型: text (基于文件名: '新文件 8 (2).txt')
2025-08-25 08:06:01.267 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | 最终识别的文档类型: text
2025-08-25 08:06:01.267 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | 成功使用 utf-8 编码解析文本内容
2025-08-25 08:06:01.268 | INFO     | 67efe682eb8a406295adbbdd0d7f39b4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbbf41967e3311f09a8488f4da8e1b91/preview/doc_name=%E6%96%B0%E6%96%87%E4%BB%B6+8+(2).txt | 38.048ms
2025-08-25 08:06:07.708 | INFO     | 2c926cd68dca4986ab415bc7fb57292f | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:06:07.710 | INFO     | 2c926cd68dca4986ab415bc7fb57292f | 成功认证Java用户: admin
2025-08-25 08:06:07.715 | INFO     | 2c926cd68dca4986ab415bc7fb57292f | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:06:07.715 | INFO     | 2c926cd68dca4986ab415bc7fb57292f | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-25 08:06:07.731 | INFO     | 2c926cd68dca4986ab415bc7fb57292f | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:06:07.733 | INFO     | 2c926cd68dca4986ab415bc7fb57292f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 24.593ms
2025-08-25 08:06:07.747 | INFO     | 75ed13c12659429988b64c1c23dbadde | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:06:07.749 | INFO     | 75ed13c12659429988b64c1c23dbadde | 成功认证Java用户: admin
2025-08-25 08:06:07.754 | INFO     | 75ed13c12659429988b64c1c23dbadde | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:06:07.755 | INFO     | 75ed13c12659429988b64c1c23dbadde | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-25 08:06:07.772 | INFO     | 75ed13c12659429988b64c1c23dbadde | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:06:07.774 | INFO     | 75ed13c12659429988b64c1c23dbadde | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 26.931ms
2025-08-25 08:06:09.500 | INFO     | 45b4a19bae554bc6ad06c7dae3057182 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:06:09.501 | INFO     | 45b4a19bae554bc6ad06c7dae3057182 | 成功认证Java用户: admin
2025-08-25 08:06:09.506 | INFO     | 45b4a19bae554bc6ad06c7dae3057182 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:06:09.506 | INFO     | 45b4a19bae554bc6ad06c7dae3057182 | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-25 08:06:09.522 | INFO     | 45b4a19bae554bc6ad06c7dae3057182 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:06:09.523 | INFO     | 45b4a19bae554bc6ad06c7dae3057182 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 24.138ms
2025-08-25 08:06:09.531 | INFO     | 3c59b07d8e4b4d6aac4822e3fff0f428 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:06:09.533 | INFO     | 3c59b07d8e4b4d6aac4822e3fff0f428 | 成功认证Java用户: admin
2025-08-25 08:06:09.537 | INFO     | 3c59b07d8e4b4d6aac4822e3fff0f428 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:06:09.538 | INFO     | 3c59b07d8e4b4d6aac4822e3fff0f428 | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-25 08:06:09.556 | INFO     | 3c59b07d8e4b4d6aac4822e3fff0f428 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:06:09.559 | INFO     | 3c59b07d8e4b4d6aac4822e3fff0f428 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 27.997ms
2025-08-25 08:06:11.633 | INFO     | 8c857efa480148068bb98e4de341d2d6 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:06:11.635 | INFO     | 8c857efa480148068bb98e4de341d2d6 | 成功认证Java用户: admin
2025-08-25 08:06:11.639 | INFO     | 8c857efa480148068bb98e4de341d2d6 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:06:11.639 | INFO     | 8c857efa480148068bb98e4de341d2d6 | 权限检查通过: user_id=1, permission=knowledge:base:update
2025-08-25 08:06:11.654 | INFO     | 8c857efa480148068bb98e4de341d2d6 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:06:11.656 | INFO     | 8c857efa480148068bb98e4de341d2d6 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 22.410ms
2025-08-25 08:06:16.234 | INFO     | 11aa3b126373401d9e55903aa0332da6 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:06:16.235 | INFO     | 11aa3b126373401d9e55903aa0332da6 | 成功认证Java用户: admin
2025-08-25 08:06:16.239 | INFO     | 11aa3b126373401d9e55903aa0332da6 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:06:16.240 | INFO     | 11aa3b126373401d9e55903aa0332da6 | 权限检查通过: user_id=1, permission=knowledge:base:update
2025-08-25 08:06:16.253 | INFO     | 11aa3b126373401d9e55903aa0332da6 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:06:16.255 | INFO     | 11aa3b126373401d9e55903aa0332da6 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 20.961ms
2025-08-25 08:06:17.900 | INFO     | 069e7893bbf14eb5a3e100305d8aff2e | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:06:17.901 | INFO     | 069e7893bbf14eb5a3e100305d8aff2e | 成功认证Java用户: admin
2025-08-25 08:06:17.906 | INFO     | 069e7893bbf14eb5a3e100305d8aff2e | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:06:17.906 | INFO     | 069e7893bbf14eb5a3e100305d8aff2e | 权限检查通过: user_id=1, permission=knowledge:base:update
2025-08-25 08:06:17.920 | INFO     | 069e7893bbf14eb5a3e100305d8aff2e | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:06:17.922 | INFO     | 069e7893bbf14eb5a3e100305d8aff2e | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbd422eb7e3311f0a5d988f4da8e1b91/parse | 22.594ms
2025-08-25 08:06:20.788 | INFO     | 4472bb9b37594457af277f2dd89cb662 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:06:20.789 | INFO     | 4472bb9b37594457af277f2dd89cb662 | 成功认证Java用户: admin
2025-08-25 08:06:20.792 | INFO     | 4472bb9b37594457af277f2dd89cb662 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:06:20.793 | INFO     | 4472bb9b37594457af277f2dd89cb662 | 权限检查通过: user_id=1, permission=knowledge:base:update
2025-08-25 08:06:20.808 | INFO     | 4472bb9b37594457af277f2dd89cb662 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:06:20.810 | INFO     | 4472bb9b37594457af277f2dd89cb662 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fba9ccdf7e3311f09b1288f4da8e1b91/parse | 22.690ms
2025-08-25 08:06:26.984 | INFO     | 5ef6abd64e0f417faef4be72464365f5 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:06:26.985 | INFO     | 5ef6abd64e0f417faef4be72464365f5 | 成功认证Java用户: admin
2025-08-25 08:06:26.993 | INFO     | 5ef6abd64e0f417faef4be72464365f5 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:06:26.993 | INFO     | 5ef6abd64e0f417faef4be72464365f5 | 权限检查通过: user_id=1, permission=knowledge:base:update
2025-08-25 08:06:27.015 | INFO     | 5ef6abd64e0f417faef4be72464365f5 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:06:27.016 | INFO     | 5ef6abd64e0f417faef4be72464365f5 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fba9ccdf7e3311f09b1288f4da8e1b91/parse | 32.674ms
2025-08-25 08:09:24.961 | INFO     | 3455511b395a4ff2962a9c8ef6783dff | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:09:24.963 | INFO     | 3455511b395a4ff2962a9c8ef6783dff | 成功认证Java用户: admin
2025-08-25 08:09:24.966 | INFO     | 3455511b395a4ff2962a9c8ef6783dff | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:09:24.966 | INFO     | 3455511b395a4ff2962a9c8ef6783dff | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-25 08:09:24.983 | INFO     | 3455511b395a4ff2962a9c8ef6783dff | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:09:24.983 | INFO     | 3455511b395a4ff2962a9c8ef6783dff | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 23.105ms
2025-08-25 08:09:25.000 | INFO     | deea4bcba72240cda4f6a2ea43eab2c1 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:09:25.001 | INFO     | deea4bcba72240cda4f6a2ea43eab2c1 | 成功认证Java用户: admin
2025-08-25 08:09:25.004 | INFO     | deea4bcba72240cda4f6a2ea43eab2c1 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:09:25.004 | INFO     | deea4bcba72240cda4f6a2ea43eab2c1 | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-25 08:09:25.018 | INFO     | deea4bcba72240cda4f6a2ea43eab2c1 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:09:25.018 | INFO     | deea4bcba72240cda4f6a2ea43eab2c1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 18.744ms
2025-08-25 08:09:28.786 | INFO     | f087bb1088bb4b9395e7464eda443cc7 | JWT标准验证成功，获取UUID: f38dcede-e916-4b11-b829-5aa0effabf5b
2025-08-25 08:09:28.787 | INFO     | f087bb1088bb4b9395e7464eda443cc7 | 成功认证Java用户: admin
2025-08-25 08:09:28.791 | INFO     | f087bb1088bb4b9395e7464eda443cc7 | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-25 08:09:28.792 | INFO     | f087bb1088bb4b9395e7464eda443cc7 | 权限检查通过: user_id=1, permission=knowledge:base:view
2025-08-25 08:09:32.062 | INFO     | f087bb1088bb4b9395e7464eda443cc7 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-25 08:09:32.063 | INFO     | f087bb1088bb4b9395e7464eda443cc7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/chunks/page=1&page_size=50 | 3277.460ms
2025-08-25 08:12:58.934 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-25 08:13:20.717 | INFO     | e18359df2fbe4faf8b9deefb1615bdce | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:13:20.719 | INFO     | e18359df2fbe4faf8b9deefb1615bdce | 成功认证Java用户: pythontest
2025-08-25 08:13:20.745 | INFO     | e18359df2fbe4faf8b9deefb1615bdce | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:13:20.746 | INFO     | e18359df2fbe4faf8b9deefb1615bdce | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:13:20.822 | INFO     | e18359df2fbe4faf8b9deefb1615bdce | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:13:20.824 | INFO     | e18359df2fbe4faf8b9deefb1615bdce | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 113.376ms
2025-08-25 08:13:20.833 | INFO     | 4fe25c0ce4b842e792190aa5f695a78c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:13:20.835 | INFO     | 4fe25c0ce4b842e792190aa5f695a78c | 成功认证Java用户: pythontest
2025-08-25 08:13:20.850 | INFO     | 4fe25c0ce4b842e792190aa5f695a78c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:13:20.851 | INFO     | 4fe25c0ce4b842e792190aa5f695a78c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:13:20.870 | INFO     | 4fe25c0ce4b842e792190aa5f695a78c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:13:20.873 | INFO     | 4fe25c0ce4b842e792190aa5f695a78c | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 39.121ms
2025-08-25 08:13:23.159 | INFO     | f38b54c8628e4f9f8574c6023a6d92ec | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:13:23.160 | INFO     | f38b54c8628e4f9f8574c6023a6d92ec | 成功认证Java用户: pythontest
2025-08-25 08:13:23.165 | INFO     | f38b54c8628e4f9f8574c6023a6d92ec | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:13:23.165 | INFO     | f38b54c8628e4f9f8574c6023a6d92ec | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:13:23.185 | INFO     | f38b54c8628e4f9f8574c6023a6d92ec | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:13:23.188 | INFO     | f38b54c8628e4f9f8574c6023a6d92ec | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 28.688ms
2025-08-25 08:13:27.643 | INFO     | a9d72d745f014795af08157a45bdf5e5 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:13:27.645 | INFO     | a9d72d745f014795af08157a45bdf5e5 | 成功认证Java用户: pythontest
2025-08-25 08:13:27.654 | INFO     | a9d72d745f014795af08157a45bdf5e5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:13:27.654 | INFO     | a9d72d745f014795af08157a45bdf5e5 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:13:27.670 | INFO     | a9d72d745f014795af08157a45bdf5e5 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:13:27.672 | INFO     | a9d72d745f014795af08157a45bdf5e5 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 29.437ms
2025-08-25 08:15:25.353 | INFO     | 229eda1c72094bb79b0c043519043bb0 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:15:25.354 | INFO     | 229eda1c72094bb79b0c043519043bb0 | 成功认证Java用户: pythontest
2025-08-25 08:15:25.375 | INFO     | 229eda1c72094bb79b0c043519043bb0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:15:25.375 | INFO     | 229eda1c72094bb79b0c043519043bb0 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-25 08:15:25.402 | INFO     | 229eda1c72094bb79b0c043519043bb0 | 文件上传完成: test.xlsx, 大小: 1152626 bytes
2025-08-25 08:15:25.662 | INFO     | 229eda1c72094bb79b0c043519043bb0 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-25 08:15:25.664 | INFO     | 229eda1c72094bb79b0c043519043bb0 | RAGFlow上传API响应: {'code': 0, 'data': [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': '992e2c42814811f0b5a888f4da8e1b91', 'location': 'test(1).xlsx', 'name': 'test(1).xlsx', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 1152626, 'thumbnail': '', 'type': 'doc'}]}
2025-08-25 08:15:25.664 | INFO     | 229eda1c72094bb79b0c043519043bb0 | 响应data类型: <class 'list'>
2025-08-25 08:15:25.665 | INFO     | 229eda1c72094bb79b0c043519043bb0 | 响应data内容: [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': '992e2c42814811f0b5a888f4da8e1b91', 'location': 'test(1).xlsx', 'name': 'test(1).xlsx', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 1152626, 'thumbnail': '', 'type': 'doc'}]
2025-08-25 08:15:25.666 | INFO     | 229eda1c72094bb79b0c043519043bb0 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 313.513ms
2025-08-25 08:15:25.678 | INFO     | 2329dbfb8d1d46c7954518d9865fe81e | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:15:25.680 | INFO     | 2329dbfb8d1d46c7954518d9865fe81e | 成功认证Java用户: pythontest
2025-08-25 08:15:25.686 | INFO     | 2329dbfb8d1d46c7954518d9865fe81e | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:15:25.687 | INFO     | 2329dbfb8d1d46c7954518d9865fe81e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:15:25.714 | INFO     | 2329dbfb8d1d46c7954518d9865fe81e | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:15:25.716 | INFO     | 2329dbfb8d1d46c7954518d9865fe81e | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 37.775ms
2025-08-25 08:15:30.726 | INFO     | d88ea63548a64e3aa919717d5ac96684 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:15:30.727 | INFO     | d88ea63548a64e3aa919717d5ac96684 | 成功认证Java用户: pythontest
2025-08-25 08:15:30.733 | INFO     | d88ea63548a64e3aa919717d5ac96684 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:15:30.734 | INFO     | d88ea63548a64e3aa919717d5ac96684 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:15:30.910 | INFO     | d88ea63548a64e3aa919717d5ac96684 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:15:30.912 | INFO     | d88ea63548a64e3aa919717d5ac96684 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/parse | 186.191ms
2025-08-25 08:15:30.917 | INFO     | 9f2c0fc71f3245a8a64040a982f5169a | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:15:30.918 | INFO     | 9f2c0fc71f3245a8a64040a982f5169a | 成功认证Java用户: pythontest
2025-08-25 08:15:30.924 | INFO     | 9f2c0fc71f3245a8a64040a982f5169a | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:15:30.924 | INFO     | 9f2c0fc71f3245a8a64040a982f5169a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:15:30.941 | INFO     | 9f2c0fc71f3245a8a64040a982f5169a | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:15:30.942 | INFO     | 9f2c0fc71f3245a8a64040a982f5169a | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 25.366ms
2025-08-25 08:15:34.940 | INFO     | a5e9be2a991c42f1a81c4dcdaa3e9946 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:15:34.941 | INFO     | a5e9be2a991c42f1a81c4dcdaa3e9946 | 成功认证Java用户: pythontest
2025-08-25 08:15:34.946 | INFO     | a5e9be2a991c42f1a81c4dcdaa3e9946 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:15:34.947 | INFO     | a5e9be2a991c42f1a81c4dcdaa3e9946 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:15:34.997 | INFO     | a5e9be2a991c42f1a81c4dcdaa3e9946 | HTTP Request: DELETE http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:15:34.997 | INFO     | a5e9be2a991c42f1a81c4dcdaa3e9946 | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/parse | 58.583ms
2025-08-25 08:15:35.004 | INFO     | c4564b3366fa483ab5205d39f7dca4dc | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:15:35.005 | INFO     | c4564b3366fa483ab5205d39f7dca4dc | 成功认证Java用户: pythontest
2025-08-25 08:15:35.011 | INFO     | c4564b3366fa483ab5205d39f7dca4dc | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:15:35.011 | INFO     | c4564b3366fa483ab5205d39f7dca4dc | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:15:35.028 | INFO     | c4564b3366fa483ab5205d39f7dca4dc | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:15:35.029 | INFO     | c4564b3366fa483ab5205d39f7dca4dc | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 25.811ms
2025-08-25 08:15:38.972 | INFO     | fa1797864a2146ac9e39ff0db1ca3085 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:15:38.973 | INFO     | fa1797864a2146ac9e39ff0db1ca3085 | 成功认证Java用户: pythontest
2025-08-25 08:15:38.978 | INFO     | fa1797864a2146ac9e39ff0db1ca3085 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:15:38.979 | INFO     | fa1797864a2146ac9e39ff0db1ca3085 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:15:39.134 | INFO     | fa1797864a2146ac9e39ff0db1ca3085 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:15:39.136 | INFO     | fa1797864a2146ac9e39ff0db1ca3085 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/parse | 164.281ms
2025-08-25 08:15:39.142 | INFO     | ab7f175f98f14c81ae216cd98b98c4cf | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:15:39.143 | INFO     | ab7f175f98f14c81ae216cd98b98c4cf | 成功认证Java用户: pythontest
2025-08-25 08:15:39.149 | INFO     | ab7f175f98f14c81ae216cd98b98c4cf | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:15:39.150 | INFO     | ab7f175f98f14c81ae216cd98b98c4cf | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:15:39.166 | INFO     | ab7f175f98f14c81ae216cd98b98c4cf | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:15:39.168 | INFO     | ab7f175f98f14c81ae216cd98b98c4cf | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 26.381ms
2025-08-25 08:15:40.955 | INFO     | a9ac9a540e8941909c8016ed8ae4e6d9 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:15:40.957 | INFO     | a9ac9a540e8941909c8016ed8ae4e6d9 | 成功认证Java用户: pythontest
2025-08-25 08:15:40.962 | INFO     | a9ac9a540e8941909c8016ed8ae4e6d9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:15:40.963 | INFO     | a9ac9a540e8941909c8016ed8ae4e6d9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:15:40.980 | INFO     | a9ac9a540e8941909c8016ed8ae4e6d9 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:15:40.982 | INFO     | a9ac9a540e8941909c8016ed8ae4e6d9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 27.534ms
2025-08-25 08:15:51.732 | INFO     | d20560afdee74187ba6ee37f7a8646f4 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:15:51.735 | INFO     | d20560afdee74187ba6ee37f7a8646f4 | 成功认证Java用户: pythontest
2025-08-25 08:15:51.740 | INFO     | d20560afdee74187ba6ee37f7a8646f4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:15:51.740 | INFO     | d20560afdee74187ba6ee37f7a8646f4 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:15:51.752 | INFO     | d20560afdee74187ba6ee37f7a8646f4 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:15:51.752 | INFO     | d20560afdee74187ba6ee37f7a8646f4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 20.969ms
2025-08-25 08:17:07.177 | INFO     | 8655a64f43d5488da58966385e56867c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:17:07.179 | INFO     | 8655a64f43d5488da58966385e56867c | 成功认证Java用户: pythontest
2025-08-25 08:17:07.183 | INFO     | 8655a64f43d5488da58966385e56867c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:17:07.183 | INFO     | 8655a64f43d5488da58966385e56867c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:17:07.230 | INFO     | 8655a64f43d5488da58966385e56867c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:17:07.231 | INFO     | 8655a64f43d5488da58966385e56867c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 53.484ms
2025-08-25 08:17:07.251 | INFO     | 993d384f675f464dbb340893b181da45 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:17:07.253 | INFO     | 993d384f675f464dbb340893b181da45 | 成功认证Java用户: pythontest
2025-08-25 08:17:07.258 | INFO     | 993d384f675f464dbb340893b181da45 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:17:07.258 | INFO     | 993d384f675f464dbb340893b181da45 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:17:07.275 | INFO     | 993d384f675f464dbb340893b181da45 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:17:07.276 | INFO     | 993d384f675f464dbb340893b181da45 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 24.789ms
2025-08-25 08:17:11.445 | INFO     | f030d0d53ec34367aed1742249043a69 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:17:11.447 | INFO     | f030d0d53ec34367aed1742249043a69 | 成功认证Java用户: pythontest
2025-08-25 08:17:11.448 | INFO     | e22914eb90654417863b3a547dc6171d | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:17:11.452 | INFO     | e22914eb90654417863b3a547dc6171d | 成功认证Java用户: pythontest
2025-08-25 08:17:11.459 | INFO     | f030d0d53ec34367aed1742249043a69 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:17:11.460 | INFO     | f030d0d53ec34367aed1742249043a69 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:17:11.474 | INFO     | e22914eb90654417863b3a547dc6171d | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:17:11.474 | INFO     | e22914eb90654417863b3a547dc6171d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:17:11.481 | INFO     | f030d0d53ec34367aed1742249043a69 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:17:11.483 | INFO     | f030d0d53ec34367aed1742249043a69 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 38.039ms
2025-08-25 08:17:11.496 | INFO     | e22914eb90654417863b3a547dc6171d | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:17:11.498 | INFO     | e22914eb90654417863b3a547dc6171d | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/4afb1382475c11f0ac3e345a603cb29c/list/page=1&page_size=100 | 50.209ms
2025-08-25 08:17:13.437 | INFO     | 473d5d80ad4743fe8ecfa24a5bb7429b | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:17:13.437 | INFO     | 5423be57fe21405bbe758dae9c219942 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:17:13.439 | INFO     | 473d5d80ad4743fe8ecfa24a5bb7429b | 成功认证Java用户: pythontest
2025-08-25 08:17:13.439 | INFO     | 5423be57fe21405bbe758dae9c219942 | 成功认证Java用户: pythontest
2025-08-25 08:17:13.444 | INFO     | 473d5d80ad4743fe8ecfa24a5bb7429b | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:17:13.445 | INFO     | 473d5d80ad4743fe8ecfa24a5bb7429b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:17:13.450 | INFO     | 5423be57fe21405bbe758dae9c219942 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:17:13.451 | INFO     | 5423be57fe21405bbe758dae9c219942 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:17:13.472 | INFO     | 473d5d80ad4743fe8ecfa24a5bb7429b | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:17:13.474 | INFO     | 473d5d80ad4743fe8ecfa24a5bb7429b | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/4afb1382475c11f0ac3e345a603cb29c/list/page=1&page_size=100 | 37.963ms
2025-08-25 08:17:13.482 | INFO     | 5423be57fe21405bbe758dae9c219942 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:17:13.483 | INFO     | 5423be57fe21405bbe758dae9c219942 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 46.378ms
2025-08-25 08:17:14.463 | INFO     | 794ff609191943699cd44b9640674227 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:17:14.464 | INFO     | 20e70912b6834b74a0af98751703632a | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:17:14.465 | INFO     | 794ff609191943699cd44b9640674227 | 成功认证Java用户: pythontest
2025-08-25 08:17:14.465 | INFO     | 20e70912b6834b74a0af98751703632a | 成功认证Java用户: pythontest
2025-08-25 08:17:14.472 | INFO     | 20e70912b6834b74a0af98751703632a | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:17:14.472 | INFO     | 20e70912b6834b74a0af98751703632a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:17:14.476 | INFO     | 794ff609191943699cd44b9640674227 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:17:14.476 | INFO     | 794ff609191943699cd44b9640674227 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:17:14.497 | INFO     | 20e70912b6834b74a0af98751703632a | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:17:14.498 | INFO     | 794ff609191943699cd44b9640674227 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:17:14.500 | INFO     | 20e70912b6834b74a0af98751703632a | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/4afb1382475c11f0ac3e345a603cb29c/list/page=1&page_size=100 | 35.854ms
2025-08-25 08:17:14.501 | INFO     | 794ff609191943699cd44b9640674227 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 37.897ms
2025-08-25 08:17:20.401 | INFO     | f4677bc583034507a6592f4f893f7887 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:17:20.403 | INFO     | f4677bc583034507a6592f4f893f7887 | 成功认证Java用户: pythontest
2025-08-25 08:17:20.409 | INFO     | f4677bc583034507a6592f4f893f7887 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:17:20.410 | INFO     | f4677bc583034507a6592f4f893f7887 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:17:20.428 | INFO     | f4677bc583034507a6592f4f893f7887 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:17:20.430 | INFO     | f4677bc583034507a6592f4f893f7887 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 29.244ms
2025-08-25 08:17:20.440 | INFO     | 13e07df1d17a46ec969b980d5fb769ed | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:17:20.441 | INFO     | 13e07df1d17a46ec969b980d5fb769ed | 成功认证Java用户: pythontest
2025-08-25 08:17:20.448 | INFO     | 13e07df1d17a46ec969b980d5fb769ed | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:17:20.448 | INFO     | 13e07df1d17a46ec969b980d5fb769ed | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:17:20.466 | INFO     | 13e07df1d17a46ec969b980d5fb769ed | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:17:20.467 | INFO     | 13e07df1d17a46ec969b980d5fb769ed | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 27.717ms
2025-08-25 08:19:08.580 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-25 08:19:19.837 | INFO     | bf8b0e19cfe6424086b5b25a7f62ea38 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:19:19.839 | INFO     | bf8b0e19cfe6424086b5b25a7f62ea38 | 成功认证Java用户: pythontest
2025-08-25 08:19:19.859 | INFO     | bf8b0e19cfe6424086b5b25a7f62ea38 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:19:19.859 | INFO     | bf8b0e19cfe6424086b5b25a7f62ea38 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:19:19.859 | INFO     | bf8b0e19cfe6424086b5b25a7f62ea38 | 使用document/run API重新解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:19:19.886 | INFO     | bf8b0e19cfe6424086b5b25a7f62ea38 | HTTP Request: POST http://192.168.66.13:9222/api/v1/document/run "HTTP/1.1 200 OK"
2025-08-25 08:19:19.887 | INFO     | bf8b0e19cfe6424086b5b25a7f62ea38 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 53.338ms
2025-08-25 08:19:26.004 | INFO     | 1affd989264a43c7ae2a79e523a42212 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:19:26.005 | INFO     | 1affd989264a43c7ae2a79e523a42212 | 成功认证Java用户: pythontest
2025-08-25 08:19:26.014 | INFO     | 1affd989264a43c7ae2a79e523a42212 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:19:26.015 | INFO     | 1affd989264a43c7ae2a79e523a42212 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:19:26.059 | INFO     | 1affd989264a43c7ae2a79e523a42212 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:19:26.060 | INFO     | 1affd989264a43c7ae2a79e523a42212 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 56.046ms
2025-08-25 08:19:26.091 | INFO     | b9e3d15365524b9594e4167ecd900d0d | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:19:26.092 | INFO     | b9e3d15365524b9594e4167ecd900d0d | 成功认证Java用户: pythontest
2025-08-25 08:19:26.096 | INFO     | b9e3d15365524b9594e4167ecd900d0d | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:19:26.096 | INFO     | b9e3d15365524b9594e4167ecd900d0d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:19:26.110 | INFO     | b9e3d15365524b9594e4167ecd900d0d | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:19:26.111 | INFO     | b9e3d15365524b9594e4167ecd900d0d | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 19.859ms
2025-08-25 08:19:30.646 | INFO     | 24c1809510374767b7e66151fde14b9b | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:19:30.647 | INFO     | 24c1809510374767b7e66151fde14b9b | 成功认证Java用户: pythontest
2025-08-25 08:19:30.653 | INFO     | 24c1809510374767b7e66151fde14b9b | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:19:30.653 | INFO     | 24c1809510374767b7e66151fde14b9b | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:19:30.654 | INFO     | 24c1809510374767b7e66151fde14b9b | 使用document/run API重新解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:19:30.663 | INFO     | 24c1809510374767b7e66151fde14b9b | HTTP Request: POST http://192.168.66.13:9222/api/v1/document/run "HTTP/1.1 200 OK"
2025-08-25 08:19:30.664 | INFO     | 24c1809510374767b7e66151fde14b9b | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 17.107ms
2025-08-25 08:20:55.131 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-25 08:20:57.949 | INFO     | bfa88eb42618494eb0ac87a5e030e847 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:20:57.951 | INFO     | bfa88eb42618494eb0ac87a5e030e847 | 成功认证Java用户: pythontest
2025-08-25 08:20:57.962 | INFO     | bfa88eb42618494eb0ac87a5e030e847 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:20:57.963 | INFO     | bfa88eb42618494eb0ac87a5e030e847 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:20:57.964 | INFO     | bfa88eb42618494eb0ac87a5e030e847 | 使用document/run API重新解析文档: fbbf41967e3311f09a8488f4da8e1b91
2025-08-25 08:20:57.981 | INFO     | bfa88eb42618494eb0ac87a5e030e847 | HTTP Request: POST http://192.168.66.13:9222/v1/document/run "HTTP/1.1 200 OK"
2025-08-25 08:20:57.983 | INFO     | bfa88eb42618494eb0ac87a5e030e847 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbbf41967e3311f09a8488f4da8e1b91/parse | 37.242ms
2025-08-25 08:22:12.526 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-25 08:22:15.535 | INFO     | 0fc922b8c1044f12ae1c7fbc60cb55df | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:22:15.537 | INFO     | 0fc922b8c1044f12ae1c7fbc60cb55df | 成功认证Java用户: pythontest
2025-08-25 08:22:15.550 | INFO     | 0fc922b8c1044f12ae1c7fbc60cb55df | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:22:15.550 | INFO     | 0fc922b8c1044f12ae1c7fbc60cb55df | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:22:15.550 | INFO     | 0fc922b8c1044f12ae1c7fbc60cb55df | 重置文档 fbd422eb7e3311f0a5d988f4da8e1b91 的状态以支持重新解析
2025-08-25 08:22:15.627 | INFO     | 0fc922b8c1044f12ae1c7fbc60cb55df | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbd422eb7e3311f0a5d988f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:22:15.628 | INFO     | 0fc922b8c1044f12ae1c7fbc60cb55df | 开始解析文档: fbd422eb7e3311f0a5d988f4da8e1b91
2025-08-25 08:22:15.639 | INFO     | 0fc922b8c1044f12ae1c7fbc60cb55df | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:22:15.640 | INFO     | 0fc922b8c1044f12ae1c7fbc60cb55df | 直接尝试解析文档: fbd422eb7e3311f0a5d988f4da8e1b91
2025-08-25 08:22:15.650 | INFO     | 0fc922b8c1044f12ae1c7fbc60cb55df | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:22:15.651 | INFO     | 0fc922b8c1044f12ae1c7fbc60cb55df | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbd422eb7e3311f0a5d988f4da8e1b91/parse | 120.264ms
2025-08-25 08:23:29.443 | INFO     | 79c36b5a21d1429db7965be7432a174f | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:23:29.446 | INFO     | 79c36b5a21d1429db7965be7432a174f | 成功认证Java用户: pythontest
2025-08-25 08:23:29.457 | INFO     | 79c36b5a21d1429db7965be7432a174f | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:23:29.457 | INFO     | 79c36b5a21d1429db7965be7432a174f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:23:29.484 | INFO     | 79c36b5a21d1429db7965be7432a174f | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:23:29.485 | INFO     | 79c36b5a21d1429db7965be7432a174f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 42.062ms
2025-08-25 08:23:29.504 | INFO     | a7dcaad7eda642d7a5963c751c98c8b0 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:23:29.505 | INFO     | a7dcaad7eda642d7a5963c751c98c8b0 | 成功认证Java用户: pythontest
2025-08-25 08:23:29.511 | INFO     | a7dcaad7eda642d7a5963c751c98c8b0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:23:29.511 | INFO     | a7dcaad7eda642d7a5963c751c98c8b0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:23:29.531 | INFO     | a7dcaad7eda642d7a5963c751c98c8b0 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:23:29.533 | INFO     | a7dcaad7eda642d7a5963c751c98c8b0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 28.900ms
2025-08-25 08:24:22.056 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-25 08:24:25.182 | INFO     | 03f3874e13f4457e943e54ca65ccd920 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:24:25.184 | INFO     | 03f3874e13f4457e943e54ca65ccd920 | 成功认证Java用户: pythontest
2025-08-25 08:24:25.198 | INFO     | 03f3874e13f4457e943e54ca65ccd920 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:24:25.198 | INFO     | 03f3874e13f4457e943e54ca65ccd920 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:24:25.232 | INFO     | 03f3874e13f4457e943e54ca65ccd920 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:24:25.233 | INFO     | 03f3874e13f4457e943e54ca65ccd920 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 54.591ms
2025-08-25 08:24:25.242 | INFO     | 58667111049745bfa509b8b245d75f28 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:24:25.243 | INFO     | 58667111049745bfa509b8b245d75f28 | 成功认证Java用户: pythontest
2025-08-25 08:24:25.254 | INFO     | 58667111049745bfa509b8b245d75f28 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:24:25.255 | INFO     | 58667111049745bfa509b8b245d75f28 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:24:25.266 | INFO     | 58667111049745bfa509b8b245d75f28 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:24:25.268 | INFO     | 58667111049745bfa509b8b245d75f28 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 25.424ms
2025-08-25 08:24:27.067 | INFO     | 2bfa3babe693498985676dc26f2cdded | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:24:27.069 | INFO     | 2bfa3babe693498985676dc26f2cdded | 成功认证Java用户: pythontest
2025-08-25 08:24:27.076 | INFO     | 2bfa3babe693498985676dc26f2cdded | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:24:27.076 | INFO     | 2bfa3babe693498985676dc26f2cdded | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:24:27.077 | INFO     | 2bfa3babe693498985676dc26f2cdded | 清空文档 992e2c42814811f0b5a888f4da8e1b91 的分块数据以支持重新解析
2025-08-25 08:24:27.471 | INFO     | 2bfa3babe693498985676dc26f2cdded | HTTP Request: DELETE http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/992e2c42814811f0b5a888f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:24:27.490 | INFO     | 2bfa3babe693498985676dc26f2cdded | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/992e2c42814811f0b5a888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:24:27.491 | INFO     | 2bfa3babe693498985676dc26f2cdded | 成功更新文档 992e2c42814811f0b5a888f4da8e1b91 的解析器配置
2025-08-25 08:24:27.492 | INFO     | 2bfa3babe693498985676dc26f2cdded | 开始解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:24:27.504 | INFO     | 2bfa3babe693498985676dc26f2cdded | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:24:27.506 | INFO     | 2bfa3babe693498985676dc26f2cdded | 直接尝试解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:24:27.518 | INFO     | 2bfa3babe693498985676dc26f2cdded | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:24:27.520 | INFO     | 2bfa3babe693498985676dc26f2cdded | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/parse | 453.331ms
2025-08-25 08:24:29.851 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:24:29.852 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | 成功认证Java用户: pythontest
2025-08-25 08:24:29.870 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:24:29.870 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:24:29.871 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | 清空文档 fbeb1a207e3311f0920888f4da8e1b91 的分块数据以支持重新解析
2025-08-25 08:24:29.999 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | HTTP Request: DELETE http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:24:30.016 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:24:30.016 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | 成功更新文档 fbeb1a207e3311f0920888f4da8e1b91 的解析器配置
2025-08-25 08:24:30.016 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:24:30.029 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:24:30.031 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:24:30.045 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:24:30.047 | INFO     | 9b62bf7e3b604e5a9caca024bde52319 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 196.776ms
2025-08-25 08:28:28.999 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-25 08:28:31.571 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:28:31.572 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | 成功认证Java用户: pythontest
2025-08-25 08:28:31.584 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:28:31.585 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:28:31.585 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | 使用文档更新API重置文档 992e2c42814811f0b5a888f4da8e1b91 的状态
2025-08-25 08:28:31.618 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/992e2c42814811f0b5a888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:28:31.619 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | 成功重置文档 992e2c42814811f0b5a888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:28:31.619 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | 开始解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:28:31.631 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:28:31.632 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | 直接尝试解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:28:31.642 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:28:31.643 | INFO     | b90dbffae0b74f3fba49e2ceb68da2e3 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/parse | 76.079ms
2025-08-25 08:28:34.974 | INFO     | bdf26abc604446aca6b34e430a33332b | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:28:34.976 | INFO     | bdf26abc604446aca6b34e430a33332b | 成功认证Java用户: pythontest
2025-08-25 08:28:34.993 | INFO     | bdf26abc604446aca6b34e430a33332b | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:28:34.994 | INFO     | bdf26abc604446aca6b34e430a33332b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:28:35.015 | INFO     | bdf26abc604446aca6b34e430a33332b | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:28:35.016 | INFO     | bdf26abc604446aca6b34e430a33332b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 41.760ms
2025-08-25 08:28:35.031 | INFO     | ffd610ff8e7b45d89a0b0fd6479f6ecf | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:28:35.033 | INFO     | ffd610ff8e7b45d89a0b0fd6479f6ecf | 成功认证Java用户: pythontest
2025-08-25 08:28:35.038 | INFO     | ffd610ff8e7b45d89a0b0fd6479f6ecf | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:28:35.039 | INFO     | ffd610ff8e7b45d89a0b0fd6479f6ecf | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:28:35.055 | INFO     | ffd610ff8e7b45d89a0b0fd6479f6ecf | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:28:35.057 | INFO     | ffd610ff8e7b45d89a0b0fd6479f6ecf | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 25.753ms
2025-08-25 08:28:39.147 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:28:39.148 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | 成功认证Java用户: pythontest
2025-08-25 08:28:39.154 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:28:39.154 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:28:39.154 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | 使用文档更新API重置文档 992e2c42814811f0b5a888f4da8e1b91 的状态
2025-08-25 08:28:39.170 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/992e2c42814811f0b5a888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:28:39.171 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | 成功重置文档 992e2c42814811f0b5a888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:28:39.171 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | 开始解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:28:39.185 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:28:39.186 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | 直接尝试解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:28:39.198 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:28:39.200 | INFO     | 5a91f0c1bae948a98e69ce44964558d0 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/parse | 53.718ms
2025-08-25 08:32:30.392 | INFO     | fb577c9d6d604d659fe533b6266d912a | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:32:30.393 | INFO     | fb577c9d6d604d659fe533b6266d912a | 成功认证Java用户: pythontest
2025-08-25 08:32:30.398 | INFO     | fb577c9d6d604d659fe533b6266d912a | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:32:30.398 | INFO     | fb577c9d6d604d659fe533b6266d912a | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:32:30.398 | INFO     | fb577c9d6d604d659fe533b6266d912a | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:32:30.412 | INFO     | fb577c9d6d604d659fe533b6266d912a | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:32:30.412 | INFO     | fb577c9d6d604d659fe533b6266d912a | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:32:30.413 | INFO     | fb577c9d6d604d659fe533b6266d912a | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:32:30.422 | INFO     | fb577c9d6d604d659fe533b6266d912a | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:30.423 | INFO     | fb577c9d6d604d659fe533b6266d912a | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:32:30.433 | INFO     | fb577c9d6d604d659fe533b6266d912a | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:30.434 | INFO     | fb577c9d6d604d659fe533b6266d912a | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 41.728ms
2025-08-25 08:32:32.376 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:32:32.377 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | 成功认证Java用户: pythontest
2025-08-25 08:32:32.383 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:32:32.383 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:32:32.384 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:32:32.399 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:32:32.400 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:32:32.400 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:32:32.412 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:32.413 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:32:32.426 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:32.428 | INFO     | 5bd4e69df7c2474c894539d05c2c6edc | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 52.382ms
2025-08-25 08:32:33.540 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:32:33.542 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | 成功认证Java用户: pythontest
2025-08-25 08:32:33.547 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:32:33.548 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:32:33.548 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | 使用文档更新API重置文档 fbd422eb7e3311f0a5d988f4da8e1b91 的状态
2025-08-25 08:32:33.563 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbd422eb7e3311f0a5d988f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:32:33.564 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | 成功重置文档 fbd422eb7e3311f0a5d988f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:32:33.565 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | 开始解析文档: fbd422eb7e3311f0a5d988f4da8e1b91
2025-08-25 08:32:33.578 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:33.579 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | 直接尝试解析文档: fbd422eb7e3311f0a5d988f4da8e1b91
2025-08-25 08:32:33.592 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:33.594 | INFO     | aeba2a8337594ba5915dbd84f6983b0d | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbd422eb7e3311f0a5d988f4da8e1b91/parse | 53.743ms
2025-08-25 08:32:36.891 | INFO     | d9dc11b16c8647d692efb910832e3896 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:32:36.893 | INFO     | d9dc11b16c8647d692efb910832e3896 | 成功认证Java用户: pythontest
2025-08-25 08:32:36.899 | INFO     | d9dc11b16c8647d692efb910832e3896 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:32:36.900 | INFO     | d9dc11b16c8647d692efb910832e3896 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:32:36.900 | INFO     | d9dc11b16c8647d692efb910832e3896 | 使用文档更新API重置文档 fba9ccdf7e3311f09b1288f4da8e1b91 的状态
2025-08-25 08:32:36.916 | INFO     | d9dc11b16c8647d692efb910832e3896 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fba9ccdf7e3311f09b1288f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:32:36.917 | INFO     | d9dc11b16c8647d692efb910832e3896 | 成功重置文档 fba9ccdf7e3311f09b1288f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:32:36.917 | INFO     | d9dc11b16c8647d692efb910832e3896 | 开始解析文档: fba9ccdf7e3311f09b1288f4da8e1b91
2025-08-25 08:32:36.930 | INFO     | d9dc11b16c8647d692efb910832e3896 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:36.932 | INFO     | d9dc11b16c8647d692efb910832e3896 | 直接尝试解析文档: fba9ccdf7e3311f09b1288f4da8e1b91
2025-08-25 08:32:36.944 | INFO     | d9dc11b16c8647d692efb910832e3896 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:36.946 | INFO     | d9dc11b16c8647d692efb910832e3896 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fba9ccdf7e3311f09b1288f4da8e1b91/parse | 54.724ms
2025-08-25 08:32:37.936 | INFO     | 482e27921d6947c1b4a7117a634671e5 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:32:37.937 | INFO     | 482e27921d6947c1b4a7117a634671e5 | 成功认证Java用户: pythontest
2025-08-25 08:32:37.943 | INFO     | 482e27921d6947c1b4a7117a634671e5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:32:37.943 | INFO     | 482e27921d6947c1b4a7117a634671e5 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:32:37.943 | INFO     | 482e27921d6947c1b4a7117a634671e5 | 使用文档更新API重置文档 fb903ac27e3311f0a32a88f4da8e1b91 的状态
2025-08-25 08:32:37.958 | INFO     | 482e27921d6947c1b4a7117a634671e5 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fb903ac27e3311f0a32a88f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:32:37.959 | INFO     | 482e27921d6947c1b4a7117a634671e5 | 成功重置文档 fb903ac27e3311f0a32a88f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:32:37.959 | INFO     | 482e27921d6947c1b4a7117a634671e5 | 开始解析文档: fb903ac27e3311f0a32a88f4da8e1b91
2025-08-25 08:32:37.972 | INFO     | 482e27921d6947c1b4a7117a634671e5 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:37.973 | INFO     | 482e27921d6947c1b4a7117a634671e5 | 直接尝试解析文档: fb903ac27e3311f0a32a88f4da8e1b91
2025-08-25 08:32:37.985 | INFO     | 482e27921d6947c1b4a7117a634671e5 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:37.987 | INFO     | 482e27921d6947c1b4a7117a634671e5 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fb903ac27e3311f0a32a88f4da8e1b91/parse | 51.489ms
2025-08-25 08:32:39.167 | INFO     | f8a9706e190641738f1a549d9a6b7522 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:32:39.168 | INFO     | f8a9706e190641738f1a549d9a6b7522 | 成功认证Java用户: pythontest
2025-08-25 08:32:39.175 | INFO     | f8a9706e190641738f1a549d9a6b7522 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:32:39.176 | INFO     | f8a9706e190641738f1a549d9a6b7522 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:32:39.176 | INFO     | f8a9706e190641738f1a549d9a6b7522 | 使用文档更新API重置文档 fb633dee7e3311f0aa0b88f4da8e1b91 的状态
2025-08-25 08:32:39.193 | INFO     | f8a9706e190641738f1a549d9a6b7522 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fb633dee7e3311f0aa0b88f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:32:39.194 | INFO     | f8a9706e190641738f1a549d9a6b7522 | 成功重置文档 fb633dee7e3311f0aa0b88f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:32:39.195 | INFO     | f8a9706e190641738f1a549d9a6b7522 | 开始解析文档: fb633dee7e3311f0aa0b88f4da8e1b91
2025-08-25 08:32:39.211 | INFO     | f8a9706e190641738f1a549d9a6b7522 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:39.212 | INFO     | f8a9706e190641738f1a549d9a6b7522 | 直接尝试解析文档: fb633dee7e3311f0aa0b88f4da8e1b91
2025-08-25 08:32:39.225 | INFO     | f8a9706e190641738f1a549d9a6b7522 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:39.227 | INFO     | f8a9706e190641738f1a549d9a6b7522 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fb633dee7e3311f0aa0b88f4da8e1b91/parse | 60.949ms
2025-08-25 08:32:40.877 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:32:40.878 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | 成功认证Java用户: pythontest
2025-08-25 08:32:40.884 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:32:40.884 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:32:40.885 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | 使用文档更新API重置文档 992e2c42814811f0b5a888f4da8e1b91 的状态
2025-08-25 08:32:40.899 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/992e2c42814811f0b5a888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:32:40.900 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | 成功重置文档 992e2c42814811f0b5a888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:32:40.900 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | 开始解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:32:40.913 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:40.914 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | 直接尝试解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:32:40.927 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:32:40.929 | INFO     | 71e84a035b90453ba3c48f2c82a6bb6f | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/parse | 51.764ms
2025-08-25 08:32:43.459 | INFO     | d81e076f40824b3aa36a76d38f468a3f | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:32:43.460 | INFO     | d81e076f40824b3aa36a76d38f468a3f | 成功认证Java用户: pythontest
2025-08-25 08:32:43.466 | INFO     | d81e076f40824b3aa36a76d38f468a3f | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:32:43.467 | INFO     | d81e076f40824b3aa36a76d38f468a3f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:32:43.484 | INFO     | d81e076f40824b3aa36a76d38f468a3f | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:32:43.487 | INFO     | d81e076f40824b3aa36a76d38f468a3f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 27.908ms
2025-08-25 08:32:43.525 | INFO     | af2a20879d814084bf51a7105b9a3f23 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:32:43.526 | INFO     | af2a20879d814084bf51a7105b9a3f23 | 成功认证Java用户: pythontest
2025-08-25 08:32:43.531 | INFO     | af2a20879d814084bf51a7105b9a3f23 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:32:43.532 | INFO     | af2a20879d814084bf51a7105b9a3f23 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:32:43.559 | INFO     | af2a20879d814084bf51a7105b9a3f23 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:32:43.560 | INFO     | af2a20879d814084bf51a7105b9a3f23 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 35.002ms
2025-08-25 08:33:04.384 | INFO     | f623c7b959ba4c30b83d3be7c20549e8 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:33:04.385 | INFO     | f623c7b959ba4c30b83d3be7c20549e8 | 成功认证Java用户: pythontest
2025-08-25 08:33:04.389 | INFO     | f623c7b959ba4c30b83d3be7c20549e8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:33:04.389 | INFO     | f623c7b959ba4c30b83d3be7c20549e8 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:33:04.431 | INFO     | f623c7b959ba4c30b83d3be7c20549e8 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:33:04.432 | INFO     | f623c7b959ba4c30b83d3be7c20549e8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 48.668ms
2025-08-25 08:33:04.449 | INFO     | 5e9e02a7df5147b7996e857f0642dd40 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:33:04.450 | INFO     | 5e9e02a7df5147b7996e857f0642dd40 | 成功认证Java用户: pythontest
2025-08-25 08:33:04.454 | INFO     | 5e9e02a7df5147b7996e857f0642dd40 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:33:04.454 | INFO     | 5e9e02a7df5147b7996e857f0642dd40 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:33:04.467 | INFO     | 5e9e02a7df5147b7996e857f0642dd40 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:33:04.467 | INFO     | 5e9e02a7df5147b7996e857f0642dd40 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 18.705ms
2025-08-25 08:33:09.388 | INFO     | 6fc25142db9247aca778d011bead2b9f | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:33:09.389 | INFO     | 6fc25142db9247aca778d011bead2b9f | 成功认证Java用户: pythontest
2025-08-25 08:33:09.394 | INFO     | 6fc25142db9247aca778d011bead2b9f | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:33:09.394 | INFO     | 6fc25142db9247aca778d011bead2b9f | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:33:09.394 | INFO     | 6fc25142db9247aca778d011bead2b9f | 使用文档更新API重置文档 992e2c42814811f0b5a888f4da8e1b91 的状态
2025-08-25 08:33:09.404 | INFO     | 6fc25142db9247aca778d011bead2b9f | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/992e2c42814811f0b5a888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:33:09.404 | INFO     | 6fc25142db9247aca778d011bead2b9f | 成功重置文档 992e2c42814811f0b5a888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:33:09.405 | INFO     | 6fc25142db9247aca778d011bead2b9f | 开始解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:33:09.415 | INFO     | 6fc25142db9247aca778d011bead2b9f | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:33:09.416 | INFO     | 6fc25142db9247aca778d011bead2b9f | 直接尝试解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:33:09.425 | INFO     | 6fc25142db9247aca778d011bead2b9f | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:33:09.427 | INFO     | 6fc25142db9247aca778d011bead2b9f | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/parse | 38.540ms
2025-08-25 08:33:11.722 | INFO     | f33fe0f2f67545b595b00299525e51a5 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:33:11.722 | INFO     | f33fe0f2f67545b595b00299525e51a5 | 成功认证Java用户: pythontest
2025-08-25 08:33:11.743 | INFO     | f33fe0f2f67545b595b00299525e51a5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:33:11.743 | INFO     | f33fe0f2f67545b595b00299525e51a5 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:33:11.743 | INFO     | f33fe0f2f67545b595b00299525e51a5 | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:33:11.757 | INFO     | f33fe0f2f67545b595b00299525e51a5 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:33:11.758 | INFO     | f33fe0f2f67545b595b00299525e51a5 | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:33:11.759 | INFO     | f33fe0f2f67545b595b00299525e51a5 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:33:11.771 | INFO     | f33fe0f2f67545b595b00299525e51a5 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:33:11.772 | INFO     | f33fe0f2f67545b595b00299525e51a5 | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:33:11.784 | INFO     | f33fe0f2f67545b595b00299525e51a5 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:33:11.785 | INFO     | f33fe0f2f67545b595b00299525e51a5 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 63.223ms
2025-08-25 08:33:15.982 | INFO     | ed11cd9ce9584ad78ad9d7d99ad22c87 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:33:15.984 | INFO     | ed11cd9ce9584ad78ad9d7d99ad22c87 | 成功认证Java用户: pythontest
2025-08-25 08:33:15.987 | INFO     | ed11cd9ce9584ad78ad9d7d99ad22c87 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:33:15.988 | INFO     | ed11cd9ce9584ad78ad9d7d99ad22c87 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:33:15.988 | INFO     | ed11cd9ce9584ad78ad9d7d99ad22c87 | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:33:17.122 | INFO     | ed11cd9ce9584ad78ad9d7d99ad22c87 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:33:17.122 | INFO     | ed11cd9ce9584ad78ad9d7d99ad22c87 | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:33:17.123 | INFO     | ed11cd9ce9584ad78ad9d7d99ad22c87 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:33:17.210 | INFO     | ed11cd9ce9584ad78ad9d7d99ad22c87 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:33:17.211 | INFO     | ed11cd9ce9584ad78ad9d7d99ad22c87 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 1229.388ms
2025-08-25 08:33:17.216 | INFO     | 9bac820075e54d8bb5aff9337ecc0bef | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:33:17.217 | INFO     | 9bac820075e54d8bb5aff9337ecc0bef | 成功认证Java用户: pythontest
2025-08-25 08:33:17.222 | INFO     | 9bac820075e54d8bb5aff9337ecc0bef | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:33:17.222 | INFO     | 9bac820075e54d8bb5aff9337ecc0bef | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:33:17.234 | INFO     | 9bac820075e54d8bb5aff9337ecc0bef | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:33:17.235 | INFO     | 9bac820075e54d8bb5aff9337ecc0bef | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 18.101ms
2025-08-25 08:33:27.248 | INFO     | 72e07d88f6be48559f20af44378f80ac | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:33:27.250 | INFO     | 72e07d88f6be48559f20af44378f80ac | 成功认证Java用户: pythontest
2025-08-25 08:33:27.254 | INFO     | 72e07d88f6be48559f20af44378f80ac | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:33:27.255 | INFO     | 72e07d88f6be48559f20af44378f80ac | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:33:27.268 | INFO     | 72e07d88f6be48559f20af44378f80ac | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:33:27.269 | INFO     | 72e07d88f6be48559f20af44378f80ac | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 21.275ms
2025-08-25 08:33:35.289 | INFO     | 982a273f53c24459b81f0586880f9a85 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:33:35.290 | INFO     | 982a273f53c24459b81f0586880f9a85 | 成功认证Java用户: pythontest
2025-08-25 08:33:35.293 | INFO     | 982a273f53c24459b81f0586880f9a85 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:33:35.294 | INFO     | 982a273f53c24459b81f0586880f9a85 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:33:35.294 | INFO     | 982a273f53c24459b81f0586880f9a85 | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:33:35.477 | INFO     | 982a273f53c24459b81f0586880f9a85 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:33:35.477 | INFO     | 982a273f53c24459b81f0586880f9a85 | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:33:35.477 | INFO     | 982a273f53c24459b81f0586880f9a85 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:33:35.583 | INFO     | 982a273f53c24459b81f0586880f9a85 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:33:35.583 | INFO     | 982a273f53c24459b81f0586880f9a85 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 295.695ms
2025-08-25 08:33:35.589 | INFO     | 311ac2a072d546e99b54f23df7702977 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:33:35.590 | INFO     | 311ac2a072d546e99b54f23df7702977 | 成功认证Java用户: pythontest
2025-08-25 08:33:35.594 | INFO     | 311ac2a072d546e99b54f23df7702977 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:33:35.595 | INFO     | 311ac2a072d546e99b54f23df7702977 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:33:35.607 | INFO     | 311ac2a072d546e99b54f23df7702977 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:33:35.607 | INFO     | 311ac2a072d546e99b54f23df7702977 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 18.915ms
2025-08-25 08:33:45.623 | INFO     | ceaf7ad4fc124fd090e02eb7d68e0ef8 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:33:45.624 | INFO     | ceaf7ad4fc124fd090e02eb7d68e0ef8 | 成功认证Java用户: pythontest
2025-08-25 08:33:45.628 | INFO     | ceaf7ad4fc124fd090e02eb7d68e0ef8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:33:45.628 | INFO     | ceaf7ad4fc124fd090e02eb7d68e0ef8 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:33:45.640 | INFO     | ceaf7ad4fc124fd090e02eb7d68e0ef8 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:33:45.641 | INFO     | ceaf7ad4fc124fd090e02eb7d68e0ef8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 18.062ms
2025-08-25 08:34:04.008 | INFO     | d48a567092124c8fb8b5401e69f2abcb | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:04.009 | INFO     | d48a567092124c8fb8b5401e69f2abcb | 成功认证Java用户: pythontest
2025-08-25 08:34:04.013 | INFO     | d48a567092124c8fb8b5401e69f2abcb | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:04.013 | INFO     | d48a567092124c8fb8b5401e69f2abcb | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:04.013 | INFO     | d48a567092124c8fb8b5401e69f2abcb | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:04.023 | INFO     | d48a567092124c8fb8b5401e69f2abcb | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:04.023 | INFO     | d48a567092124c8fb8b5401e69f2abcb | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:04.023 | INFO     | d48a567092124c8fb8b5401e69f2abcb | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:04.033 | INFO     | d48a567092124c8fb8b5401e69f2abcb | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:04.034 | INFO     | d48a567092124c8fb8b5401e69f2abcb | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:04.042 | INFO     | d48a567092124c8fb8b5401e69f2abcb | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:04.043 | INFO     | d48a567092124c8fb8b5401e69f2abcb | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 35.793ms
2025-08-25 08:34:41.078 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:41.080 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | 成功认证Java用户: pythontest
2025-08-25 08:34:41.084 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:41.084 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:41.084 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:41.116 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:41.117 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:41.117 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:41.127 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:41.128 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:41.138 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:41.139 | INFO     | 7c278f2fd4744141994ff7eac0ffb966 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 61.050ms
2025-08-25 08:34:42.649 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:42.650 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | 成功认证Java用户: pythontest
2025-08-25 08:34:42.654 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:42.654 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:42.655 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:42.726 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:42.727 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:42.727 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:42.736 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:42.737 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:42.747 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:42.748 | INFO     | d4159e44e4794273b5b9f9ad98f24c0d | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 99.802ms
2025-08-25 08:34:43.221 | INFO     | c708984df7094d24b031fb15f0a50afa | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:43.222 | INFO     | c708984df7094d24b031fb15f0a50afa | 成功认证Java用户: pythontest
2025-08-25 08:34:43.226 | INFO     | c708984df7094d24b031fb15f0a50afa | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:43.226 | INFO     | c708984df7094d24b031fb15f0a50afa | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:43.227 | INFO     | c708984df7094d24b031fb15f0a50afa | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:43.257 | INFO     | c708984df7094d24b031fb15f0a50afa | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:43.257 | INFO     | c708984df7094d24b031fb15f0a50afa | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:43.257 | INFO     | c708984df7094d24b031fb15f0a50afa | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:43.268 | INFO     | c708984df7094d24b031fb15f0a50afa | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:43.269 | INFO     | c708984df7094d24b031fb15f0a50afa | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:43.278 | INFO     | c708984df7094d24b031fb15f0a50afa | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:43.279 | INFO     | c708984df7094d24b031fb15f0a50afa | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 58.567ms
2025-08-25 08:34:43.563 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:43.564 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | 成功认证Java用户: pythontest
2025-08-25 08:34:43.568 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:43.568 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:43.568 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:43.591 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:43.591 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:43.592 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:43.601 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:43.602 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:43.612 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:43.613 | INFO     | 009b48a13dbe48bda3fb2bbe99f34578 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 50.705ms
2025-08-25 08:34:43.867 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:43.868 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | 成功认证Java用户: pythontest
2025-08-25 08:34:43.872 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:43.872 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:43.873 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:43.987 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:43.987 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:43.988 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:44.000 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:44.001 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:44.012 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:44.013 | INFO     | 952b98389b2140bb8c8f6adae1a4f571 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 146.349ms
2025-08-25 08:34:48.381 | INFO     | 749525b028e0487f87df0284739e9f41 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:48.383 | INFO     | 749525b028e0487f87df0284739e9f41 | 成功认证Java用户: pythontest
2025-08-25 08:34:48.387 | INFO     | 749525b028e0487f87df0284739e9f41 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:48.387 | INFO     | 749525b028e0487f87df0284739e9f41 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:48.387 | INFO     | 749525b028e0487f87df0284739e9f41 | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:48.398 | INFO     | 749525b028e0487f87df0284739e9f41 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:48.399 | INFO     | 749525b028e0487f87df0284739e9f41 | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:48.399 | INFO     | 749525b028e0487f87df0284739e9f41 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:48.409 | INFO     | 749525b028e0487f87df0284739e9f41 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:48.411 | INFO     | 749525b028e0487f87df0284739e9f41 | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:48.426 | INFO     | 749525b028e0487f87df0284739e9f41 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:48.427 | INFO     | 749525b028e0487f87df0284739e9f41 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 46.012ms
2025-08-25 08:34:53.832 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:53.834 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | 成功认证Java用户: pythontest
2025-08-25 08:34:53.839 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:53.839 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:53.839 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:53.883 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:53.883 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:53.883 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:53.892 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:53.893 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:53.902 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:53.903 | INFO     | 46d2711c2ad947d9b054f8ddf0b976d4 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 71.144ms
2025-08-25 08:34:54.518 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:54.519 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | 成功认证Java用户: pythontest
2025-08-25 08:34:54.524 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:54.524 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:54.524 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:54.549 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:54.550 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:54.550 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:54.560 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:54.561 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:54.570 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:54.571 | INFO     | 59fe1cc2aec944f188aabc6b9c454712 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 52.999ms
2025-08-25 08:34:55.037 | INFO     | e80c53a4e2e943a99570506b8e973fac | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:55.038 | INFO     | e80c53a4e2e943a99570506b8e973fac | 成功认证Java用户: pythontest
2025-08-25 08:34:55.042 | INFO     | e80c53a4e2e943a99570506b8e973fac | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:55.042 | INFO     | e80c53a4e2e943a99570506b8e973fac | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:55.042 | INFO     | e80c53a4e2e943a99570506b8e973fac | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:55.087 | INFO     | e80c53a4e2e943a99570506b8e973fac | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:55.087 | INFO     | e80c53a4e2e943a99570506b8e973fac | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:55.087 | INFO     | e80c53a4e2e943a99570506b8e973fac | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:55.096 | INFO     | e80c53a4e2e943a99570506b8e973fac | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:55.097 | INFO     | e80c53a4e2e943a99570506b8e973fac | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:55.106 | INFO     | e80c53a4e2e943a99570506b8e973fac | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:55.107 | INFO     | e80c53a4e2e943a99570506b8e973fac | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 70.184ms
2025-08-25 08:34:55.349 | INFO     | 93334a829045486891a68f9d9a017856 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:55.351 | INFO     | 93334a829045486891a68f9d9a017856 | 成功认证Java用户: pythontest
2025-08-25 08:34:55.355 | INFO     | 93334a829045486891a68f9d9a017856 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:55.356 | INFO     | 93334a829045486891a68f9d9a017856 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:55.356 | INFO     | 93334a829045486891a68f9d9a017856 | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:55.388 | INFO     | 93334a829045486891a68f9d9a017856 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:55.388 | INFO     | 93334a829045486891a68f9d9a017856 | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:55.389 | INFO     | 93334a829045486891a68f9d9a017856 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:55.398 | INFO     | 93334a829045486891a68f9d9a017856 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:55.399 | INFO     | 93334a829045486891a68f9d9a017856 | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:55.408 | INFO     | 93334a829045486891a68f9d9a017856 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:55.409 | INFO     | 93334a829045486891a68f9d9a017856 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 60.039ms
2025-08-25 08:34:55.592 | INFO     | 15d0936b09ef41a09b053abb2370a551 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:34:55.593 | INFO     | 15d0936b09ef41a09b053abb2370a551 | 成功认证Java用户: pythontest
2025-08-25 08:34:55.597 | INFO     | 15d0936b09ef41a09b053abb2370a551 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:34:55.597 | INFO     | 15d0936b09ef41a09b053abb2370a551 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:34:55.597 | INFO     | 15d0936b09ef41a09b053abb2370a551 | 使用文档更新API重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态
2025-08-25 08:34:55.695 | INFO     | 15d0936b09ef41a09b053abb2370a551 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:34:55.695 | INFO     | 15d0936b09ef41a09b053abb2370a551 | 成功重置文档 fbeb1a207e3311f0920888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:34:55.696 | INFO     | 15d0936b09ef41a09b053abb2370a551 | 开始解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:55.705 | INFO     | 15d0936b09ef41a09b053abb2370a551 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:55.705 | INFO     | 15d0936b09ef41a09b053abb2370a551 | 直接尝试解析文档: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 08:34:55.715 | INFO     | 15d0936b09ef41a09b053abb2370a551 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:34:55.717 | INFO     | 15d0936b09ef41a09b053abb2370a551 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/parse | 124.785ms
2025-08-25 08:38:02.055 | INFO     | 81657c5644004c61a77b50320dec1ae3 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:02.056 | INFO     | 81657c5644004c61a77b50320dec1ae3 | 成功认证Java用户: pythontest
2025-08-25 08:38:02.061 | INFO     | 81657c5644004c61a77b50320dec1ae3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:02.061 | INFO     | 81657c5644004c61a77b50320dec1ae3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:02.080 | INFO     | 81657c5644004c61a77b50320dec1ae3 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:02.082 | INFO     | 81657c5644004c61a77b50320dec1ae3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 26.755ms
2025-08-25 08:38:02.112 | INFO     | 43fd94f36f4445af8302201af5869f93 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:02.113 | INFO     | 43fd94f36f4445af8302201af5869f93 | 成功认证Java用户: pythontest
2025-08-25 08:38:02.116 | INFO     | 43fd94f36f4445af8302201af5869f93 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:02.116 | INFO     | 43fd94f36f4445af8302201af5869f93 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:02.128 | INFO     | 43fd94f36f4445af8302201af5869f93 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:02.129 | INFO     | 43fd94f36f4445af8302201af5869f93 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 16.996ms
2025-08-25 08:38:11.269 | INFO     | 94bcd9aae1c0472f8b84f836e43b9630 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:11.270 | INFO     | 94bcd9aae1c0472f8b84f836e43b9630 | 成功认证Java用户: pythontest
2025-08-25 08:38:11.274 | INFO     | 94bcd9aae1c0472f8b84f836e43b9630 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:11.274 | INFO     | 94bcd9aae1c0472f8b84f836e43b9630 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:11.285 | INFO     | 94bcd9aae1c0472f8b84f836e43b9630 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:11.286 | INFO     | 94bcd9aae1c0472f8b84f836e43b9630 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 16.859ms
2025-08-25 08:38:11.318 | INFO     | 22a58f0ace544792ac6bc750a09a311c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:11.320 | INFO     | 22a58f0ace544792ac6bc750a09a311c | 成功认证Java用户: pythontest
2025-08-25 08:38:11.324 | INFO     | 22a58f0ace544792ac6bc750a09a311c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:11.324 | INFO     | 22a58f0ace544792ac6bc750a09a311c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:11.335 | INFO     | 22a58f0ace544792ac6bc750a09a311c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:11.336 | INFO     | 22a58f0ace544792ac6bc750a09a311c | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 17.561ms
2025-08-25 08:38:23.250 | INFO     | e96585ad61b84a78aeaf2cdcf3cc5657 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:23.250 | INFO     | e96585ad61b84a78aeaf2cdcf3cc5657 | 成功认证Java用户: pythontest
2025-08-25 08:38:23.254 | INFO     | e96585ad61b84a78aeaf2cdcf3cc5657 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:23.255 | INFO     | e96585ad61b84a78aeaf2cdcf3cc5657 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:23.265 | INFO     | e96585ad61b84a78aeaf2cdcf3cc5657 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:23.266 | INFO     | e96585ad61b84a78aeaf2cdcf3cc5657 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 16.592ms
2025-08-25 08:38:23.292 | INFO     | 6037838f938348ca9d2a8cc613609129 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:23.293 | INFO     | 6037838f938348ca9d2a8cc613609129 | 成功认证Java用户: pythontest
2025-08-25 08:38:23.297 | INFO     | 6037838f938348ca9d2a8cc613609129 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:23.297 | INFO     | 6037838f938348ca9d2a8cc613609129 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:23.309 | INFO     | 6037838f938348ca9d2a8cc613609129 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:23.310 | INFO     | 6037838f938348ca9d2a8cc613609129 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 17.850ms
2025-08-25 08:38:29.642 | INFO     | 3f5e0e501bd943bf8c6f25efed1787f9 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:29.643 | INFO     | 3f5e0e501bd943bf8c6f25efed1787f9 | 成功认证Java用户: pythontest
2025-08-25 08:38:29.652 | INFO     | 3f5e0e501bd943bf8c6f25efed1787f9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:29.652 | INFO     | 3f5e0e501bd943bf8c6f25efed1787f9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:29.670 | INFO     | 3f5e0e501bd943bf8c6f25efed1787f9 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:29.672 | INFO     | 3f5e0e501bd943bf8c6f25efed1787f9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 29.099ms
2025-08-25 08:38:29.681 | INFO     | 2c25b5994ac34915a986a2e08ed53bbb | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:29.683 | INFO     | 2c25b5994ac34915a986a2e08ed53bbb | 成功认证Java用户: pythontest
2025-08-25 08:38:29.689 | INFO     | 2c25b5994ac34915a986a2e08ed53bbb | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:29.690 | INFO     | 2c25b5994ac34915a986a2e08ed53bbb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:29.706 | INFO     | 2c25b5994ac34915a986a2e08ed53bbb | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:29.708 | INFO     | 2c25b5994ac34915a986a2e08ed53bbb | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 27.190ms
2025-08-25 08:38:31.734 | INFO     | b79989d8d49146f1a2be577fb07d551d | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:31.735 | INFO     | b79989d8d49146f1a2be577fb07d551d | 成功认证Java用户: pythontest
2025-08-25 08:38:31.740 | INFO     | b79989d8d49146f1a2be577fb07d551d | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:31.740 | INFO     | b79989d8d49146f1a2be577fb07d551d | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:38:31.741 | INFO     | b79989d8d49146f1a2be577fb07d551d | 使用文档更新API重置文档 992e2c42814811f0b5a888f4da8e1b91 的状态
2025-08-25 08:38:31.755 | INFO     | b79989d8d49146f1a2be577fb07d551d | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/992e2c42814811f0b5a888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:38:31.755 | INFO     | b79989d8d49146f1a2be577fb07d551d | 成功重置文档 992e2c42814811f0b5a888f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:38:31.755 | INFO     | b79989d8d49146f1a2be577fb07d551d | 开始解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:38:31.769 | INFO     | b79989d8d49146f1a2be577fb07d551d | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:38:31.771 | INFO     | b79989d8d49146f1a2be577fb07d551d | 直接尝试解析文档: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:38:31.788 | INFO     | b79989d8d49146f1a2be577fb07d551d | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:38:31.790 | INFO     | b79989d8d49146f1a2be577fb07d551d | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/parse | 56.290ms
2025-08-25 08:38:42.660 | INFO     | c6601b639df142098926db650f97e4d2 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:42.661 | INFO     | c6601b639df142098926db650f97e4d2 | 成功认证Java用户: pythontest
2025-08-25 08:38:42.666 | INFO     | c6601b639df142098926db650f97e4d2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:42.666 | INFO     | c6601b639df142098926db650f97e4d2 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 08:38:42.712 | INFO     | c6601b639df142098926db650f97e4d2 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/992e2c42814811f0b5a888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:38:42.718 | INFO     | c6601b639df142098926db650f97e4d2 | 文档预览调试 - doc_id: 992e2c42814811f0b5a888f4da8e1b91
2025-08-25 08:38:42.718 | INFO     | c6601b639df142098926db650f97e4d2 | doc_name: 'test(1).xlsx', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-25 08:38:42.719 | INFO     | c6601b639df142098926db650f97e4d2 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-25 08:38:42.719 | INFO     | c6601b639df142098926db650f97e4d2 | 根据文件头检测到Office文档格式(ZIP)，修正类型为office
2025-08-25 08:38:42.719 | INFO     | c6601b639df142098926db650f97e4d2 | 最终识别的文档类型: office
2025-08-25 08:38:42.720 | INFO     | c6601b639df142098926db650f97e4d2 | 检测到Office文档: test(1).xlsx，返回原始文档URL用于vue-office预览
2025-08-25 08:38:42.720 | INFO     | c6601b639df142098926db650f97e4d2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/preview/doc_name=test(1).xlsx | 60.595ms
2025-08-25 08:38:42.728 | INFO     | eafd992e0c3b478fb04c2879ae392867 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:42.729 | INFO     | eafd992e0c3b478fb04c2879ae392867 | 成功认证Java用户: pythontest
2025-08-25 08:38:42.737 | INFO     | eafd992e0c3b478fb04c2879ae392867 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:42.737 | INFO     | eafd992e0c3b478fb04c2879ae392867 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 08:38:42.787 | INFO     | eafd992e0c3b478fb04c2879ae392867 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/992e2c42814811f0b5a888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:38:42.828 | INFO     | eafd992e0c3b478fb04c2879ae392867 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/992e2c42814811f0b5a888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:38:42.833 | INFO     | eafd992e0c3b478fb04c2879ae392867 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/content | 104.164ms
2025-08-25 08:38:45.429 | INFO     | e5b45ad19d5442d1b245d78575919da2 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:45.430 | INFO     | e5b45ad19d5442d1b245d78575919da2 | 成功认证Java用户: pythontest
2025-08-25 08:38:45.436 | INFO     | e5b45ad19d5442d1b245d78575919da2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:45.436 | INFO     | e5b45ad19d5442d1b245d78575919da2 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:45.451 | INFO     | e5b45ad19d5442d1b245d78575919da2 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:45.453 | INFO     | e5b45ad19d5442d1b245d78575919da2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 24.032ms
2025-08-25 08:38:45.469 | INFO     | 468948b2384b4d19ab9e3179483b9356 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:45.470 | INFO     | 468948b2384b4d19ab9e3179483b9356 | 成功认证Java用户: pythontest
2025-08-25 08:38:45.478 | INFO     | 468948b2384b4d19ab9e3179483b9356 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:45.479 | INFO     | 468948b2384b4d19ab9e3179483b9356 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:45.500 | INFO     | 468948b2384b4d19ab9e3179483b9356 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:45.502 | INFO     | 468948b2384b4d19ab9e3179483b9356 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 33.191ms
2025-08-25 08:38:46.607 | INFO     | 60c855c2ffb5483e9df82ea529f9b326 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:46.608 | INFO     | 60c855c2ffb5483e9df82ea529f9b326 | 成功认证Java用户: pythontest
2025-08-25 08:38:46.613 | INFO     | 60c855c2ffb5483e9df82ea529f9b326 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:46.613 | INFO     | 60c855c2ffb5483e9df82ea529f9b326 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 08:38:46.796 | INFO     | 60c855c2ffb5483e9df82ea529f9b326 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/992e2c42814811f0b5a888f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-25 08:38:46.798 | INFO     | 60c855c2ffb5483e9df82ea529f9b326 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/992e2c42814811f0b5a888f4da8e1b91/chunks/page=1&page_size=50 | 191.007ms
2025-08-25 08:38:55.572 | INFO     | 4009b50911fc4a01b2aa2e9b2f4db235 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:55.573 | INFO     | 4009b50911fc4a01b2aa2e9b2f4db235 | 成功认证Java用户: pythontest
2025-08-25 08:38:55.577 | INFO     | 4009b50911fc4a01b2aa2e9b2f4db235 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:55.577 | INFO     | 4009b50911fc4a01b2aa2e9b2f4db235 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:55.594 | INFO     | 4009b50911fc4a01b2aa2e9b2f4db235 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:55.595 | INFO     | 4009b50911fc4a01b2aa2e9b2f4db235 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 23.722ms
2025-08-25 08:38:55.606 | INFO     | b7af161b79d444debb1922ecdff2df0f | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:55.607 | INFO     | b7af161b79d444debb1922ecdff2df0f | 成功认证Java用户: pythontest
2025-08-25 08:38:55.614 | INFO     | b7af161b79d444debb1922ecdff2df0f | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:55.615 | INFO     | b7af161b79d444debb1922ecdff2df0f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:55.632 | INFO     | b7af161b79d444debb1922ecdff2df0f | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:55.634 | INFO     | b7af161b79d444debb1922ecdff2df0f | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 28.416ms
2025-08-25 08:38:57.302 | INFO     | 1de4c5d32f8343548b2dc4506a109501 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:57.303 | INFO     | 1de4c5d32f8343548b2dc4506a109501 | 成功认证Java用户: pythontest
2025-08-25 08:38:57.311 | INFO     | 1de4c5d32f8343548b2dc4506a109501 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:57.312 | INFO     | 1de4c5d32f8343548b2dc4506a109501 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:57.328 | INFO     | 1de4c5d32f8343548b2dc4506a109501 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:57.329 | INFO     | 1de4c5d32f8343548b2dc4506a109501 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 27.658ms
2025-08-25 08:38:57.343 | INFO     | 0d04eaf0d738497d9910bae733919cf9 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:38:57.345 | INFO     | 0d04eaf0d738497d9910bae733919cf9 | 成功认证Java用户: pythontest
2025-08-25 08:38:57.351 | INFO     | 0d04eaf0d738497d9910bae733919cf9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:38:57.352 | INFO     | 0d04eaf0d738497d9910bae733919cf9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:38:57.370 | INFO     | 0d04eaf0d738497d9910bae733919cf9 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:38:57.372 | INFO     | 0d04eaf0d738497d9910bae733919cf9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 29.314ms
2025-08-25 08:39:02.124 | INFO     | 99f3259ceea9414b870a977b656f2db0 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:02.126 | INFO     | 99f3259ceea9414b870a977b656f2db0 | 成功认证Java用户: pythontest
2025-08-25 08:39:02.137 | INFO     | 99f3259ceea9414b870a977b656f2db0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:02.138 | INFO     | 99f3259ceea9414b870a977b656f2db0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:02.153 | INFO     | 99f3259ceea9414b870a977b656f2db0 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:02.155 | INFO     | 99f3259ceea9414b870a977b656f2db0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 30.556ms
2025-08-25 08:39:02.162 | INFO     | 451c925fc7484a2eba750a32dc881505 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:02.163 | INFO     | 451c925fc7484a2eba750a32dc881505 | 成功认证Java用户: pythontest
2025-08-25 08:39:02.169 | INFO     | 451c925fc7484a2eba750a32dc881505 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:02.169 | INFO     | 451c925fc7484a2eba750a32dc881505 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:02.186 | INFO     | 451c925fc7484a2eba750a32dc881505 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:02.188 | INFO     | 451c925fc7484a2eba750a32dc881505 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 25.885ms
2025-08-25 08:39:06.215 | INFO     | ea47dd30058046f0858909bf7830d9df | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:06.217 | INFO     | ea47dd30058046f0858909bf7830d9df | 成功认证Java用户: pythontest
2025-08-25 08:39:06.221 | INFO     | ea47dd30058046f0858909bf7830d9df | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:06.222 | INFO     | ea47dd30058046f0858909bf7830d9df | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:06.237 | INFO     | ea47dd30058046f0858909bf7830d9df | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:06.238 | INFO     | ea47dd30058046f0858909bf7830d9df | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 23.393ms
2025-08-25 08:39:06.252 | INFO     | da1ba1d1ac8f4df2b6bb8b0114cb2b27 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:06.254 | INFO     | da1ba1d1ac8f4df2b6bb8b0114cb2b27 | 成功认证Java用户: pythontest
2025-08-25 08:39:06.259 | INFO     | da1ba1d1ac8f4df2b6bb8b0114cb2b27 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:06.259 | INFO     | da1ba1d1ac8f4df2b6bb8b0114cb2b27 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:06.277 | INFO     | da1ba1d1ac8f4df2b6bb8b0114cb2b27 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:06.278 | INFO     | da1ba1d1ac8f4df2b6bb8b0114cb2b27 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 25.930ms
2025-08-25 08:39:06.862 | INFO     | cfac972103754a21b4ab741f7f51ce1c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:06.863 | INFO     | cfac972103754a21b4ab741f7f51ce1c | 成功认证Java用户: pythontest
2025-08-25 08:39:06.869 | INFO     | cfac972103754a21b4ab741f7f51ce1c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:06.871 | INFO     | cfac972103754a21b4ab741f7f51ce1c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:06.886 | INFO     | cfac972103754a21b4ab741f7f51ce1c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:06.888 | INFO     | cfac972103754a21b4ab741f7f51ce1c | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 26.037ms
2025-08-25 08:39:14.805 | INFO     | 5e3e1f38b76f4c64a0c2b197efadb6da | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:14.806 | INFO     | 5e3e1f38b76f4c64a0c2b197efadb6da | 成功认证Java用户: pythontest
2025-08-25 08:39:14.812 | INFO     | 5e3e1f38b76f4c64a0c2b197efadb6da | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:14.813 | INFO     | 5e3e1f38b76f4c64a0c2b197efadb6da | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:14.829 | INFO     | 5e3e1f38b76f4c64a0c2b197efadb6da | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:14.830 | INFO     | 5e3e1f38b76f4c64a0c2b197efadb6da | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 26.099ms
2025-08-25 08:39:14.838 | INFO     | 45cba7e4965a434abb76426d41f56139 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:14.840 | INFO     | 45cba7e4965a434abb76426d41f56139 | 成功认证Java用户: pythontest
2025-08-25 08:39:14.845 | INFO     | 45cba7e4965a434abb76426d41f56139 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:14.846 | INFO     | 45cba7e4965a434abb76426d41f56139 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:14.863 | INFO     | 45cba7e4965a434abb76426d41f56139 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:14.865 | INFO     | 45cba7e4965a434abb76426d41f56139 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 26.566ms
2025-08-25 08:39:15.997 | INFO     | 50f480d3c74a466181f84e70777d635a | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:15.998 | INFO     | 50f480d3c74a466181f84e70777d635a | 成功认证Java用户: pythontest
2025-08-25 08:39:16.003 | INFO     | 50f480d3c74a466181f84e70777d635a | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:16.003 | INFO     | 50f480d3c74a466181f84e70777d635a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:16.019 | INFO     | 50f480d3c74a466181f84e70777d635a | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:16.020 | INFO     | 50f480d3c74a466181f84e70777d635a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 24.253ms
2025-08-25 08:39:16.034 | INFO     | 8da9256f59ee4b96861334f43b5cd9b3 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:16.036 | INFO     | 8da9256f59ee4b96861334f43b5cd9b3 | 成功认证Java用户: pythontest
2025-08-25 08:39:16.042 | INFO     | 8da9256f59ee4b96861334f43b5cd9b3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:16.043 | INFO     | 8da9256f59ee4b96861334f43b5cd9b3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:16.061 | INFO     | 8da9256f59ee4b96861334f43b5cd9b3 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:16.063 | INFO     | 8da9256f59ee4b96861334f43b5cd9b3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 28.688ms
2025-08-25 08:39:17.285 | INFO     | 6f4b4b3b8e09405c8fcd6426b7ecbf9e | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:17.287 | INFO     | 6f4b4b3b8e09405c8fcd6426b7ecbf9e | 成功认证Java用户: pythontest
2025-08-25 08:39:17.297 | INFO     | 6f4b4b3b8e09405c8fcd6426b7ecbf9e | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:17.297 | INFO     | 6f4b4b3b8e09405c8fcd6426b7ecbf9e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:17.313 | INFO     | 6f4b4b3b8e09405c8fcd6426b7ecbf9e | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:17.315 | INFO     | 6f4b4b3b8e09405c8fcd6426b7ecbf9e | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 30.252ms
2025-08-25 08:39:18.222 | INFO     | 552eecfa2bc046498dcf1c729e9c92a3 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:18.223 | INFO     | 552eecfa2bc046498dcf1c729e9c92a3 | 成功认证Java用户: pythontest
2025-08-25 08:39:18.229 | INFO     | 552eecfa2bc046498dcf1c729e9c92a3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:18.229 | INFO     | 552eecfa2bc046498dcf1c729e9c92a3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:18.245 | INFO     | 552eecfa2bc046498dcf1c729e9c92a3 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:18.247 | INFO     | 552eecfa2bc046498dcf1c729e9c92a3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 25.263ms
2025-08-25 08:39:18.714 | INFO     | 44ad60800d62411c80668f7d5194295f | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:18.716 | INFO     | 44ad60800d62411c80668f7d5194295f | 成功认证Java用户: pythontest
2025-08-25 08:39:18.723 | INFO     | 44ad60800d62411c80668f7d5194295f | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:18.723 | INFO     | 44ad60800d62411c80668f7d5194295f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:18.738 | INFO     | 44ad60800d62411c80668f7d5194295f | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:18.740 | INFO     | 44ad60800d62411c80668f7d5194295f | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 26.146ms
2025-08-25 08:39:19.007 | INFO     | bdef45be0cd9487ca65072f9b3c8be4f | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:19.009 | INFO     | bdef45be0cd9487ca65072f9b3c8be4f | 成功认证Java用户: pythontest
2025-08-25 08:39:19.015 | INFO     | bdef45be0cd9487ca65072f9b3c8be4f | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:19.015 | INFO     | bdef45be0cd9487ca65072f9b3c8be4f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:19.031 | INFO     | bdef45be0cd9487ca65072f9b3c8be4f | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:19.032 | INFO     | bdef45be0cd9487ca65072f9b3c8be4f | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 25.705ms
2025-08-25 08:39:19.199 | INFO     | 088bdd163a4a4763ba7410a7c7bb1d83 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:19.201 | INFO     | 088bdd163a4a4763ba7410a7c7bb1d83 | 成功认证Java用户: pythontest
2025-08-25 08:39:19.206 | INFO     | 088bdd163a4a4763ba7410a7c7bb1d83 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:19.207 | INFO     | 088bdd163a4a4763ba7410a7c7bb1d83 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:19.221 | INFO     | 088bdd163a4a4763ba7410a7c7bb1d83 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:19.223 | INFO     | 088bdd163a4a4763ba7410a7c7bb1d83 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 23.465ms
2025-08-25 08:39:48.605 | INFO     | 2fa0c14865b744d2a04291491098721d | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:39:48.606 | INFO     | 2fa0c14865b744d2a04291491098721d | 成功认证Java用户: pythontest
2025-08-25 08:39:48.611 | INFO     | 2fa0c14865b744d2a04291491098721d | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:39:48.611 | INFO     | 2fa0c14865b744d2a04291491098721d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:39:48.635 | INFO     | 2fa0c14865b744d2a04291491098721d | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:39:48.635 | INFO     | 2fa0c14865b744d2a04291491098721d | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 30.462ms
2025-08-25 08:40:02.339 | INFO     | e42a9cb0adba45a49a0fbbfd5f44b40d | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:02.341 | INFO     | e42a9cb0adba45a49a0fbbfd5f44b40d | 成功认证Java用户: pythontest
2025-08-25 08:40:02.348 | INFO     | e42a9cb0adba45a49a0fbbfd5f44b40d | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:02.348 | INFO     | e42a9cb0adba45a49a0fbbfd5f44b40d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:40:02.373 | INFO     | e42a9cb0adba45a49a0fbbfd5f44b40d | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:40:02.374 | INFO     | e42a9cb0adba45a49a0fbbfd5f44b40d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 35.930ms
2025-08-25 08:40:02.383 | INFO     | 6345decc8815462596c36c7ca9e84eb2 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:02.384 | INFO     | 6345decc8815462596c36c7ca9e84eb2 | 成功认证Java用户: pythontest
2025-08-25 08:40:02.391 | INFO     | 6345decc8815462596c36c7ca9e84eb2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:02.391 | INFO     | 6345decc8815462596c36c7ca9e84eb2 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:40:02.410 | INFO     | 6345decc8815462596c36c7ca9e84eb2 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:40:02.411 | INFO     | 6345decc8815462596c36c7ca9e84eb2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 27.963ms
2025-08-25 08:40:06.298 | INFO     | 59643f0209af4a698064ab25a104bb10 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:06.299 | INFO     | 59643f0209af4a698064ab25a104bb10 | 成功认证Java用户: pythontest
2025-08-25 08:40:06.304 | INFO     | 59643f0209af4a698064ab25a104bb10 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:06.305 | INFO     | 59643f0209af4a698064ab25a104bb10 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:40:06.321 | INFO     | 59643f0209af4a698064ab25a104bb10 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:40:06.323 | INFO     | 59643f0209af4a698064ab25a104bb10 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 25.536ms
2025-08-25 08:40:15.996 | INFO     | cc24cf51f14e412483bbca7ed835f749 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:15.997 | INFO     | cc24cf51f14e412483bbca7ed835f749 | 成功认证Java用户: pythontest
2025-08-25 08:40:16.182 | INFO     | cc24cf51f14e412483bbca7ed835f749 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:16.184 | INFO     | cc24cf51f14e412483bbca7ed835f749 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-25 08:40:16.442 | INFO     | cc24cf51f14e412483bbca7ed835f749 | 文件上传完成: test (1).pptx, 大小: 11122759 bytes
2025-08-25 08:40:16.700 | INFO     | cc24cf51f14e412483bbca7ed835f749 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-25 08:40:16.704 | INFO     | cc24cf51f14e412483bbca7ed835f749 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 708.200ms
2025-08-25 08:40:16.711 | INFO     | b61e06c12fb74ca38e54f5e2cf29e4dd | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:16.712 | INFO     | b61e06c12fb74ca38e54f5e2cf29e4dd | 成功认证Java用户: pythontest
2025-08-25 08:40:16.719 | INFO     | b61e06c12fb74ca38e54f5e2cf29e4dd | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:16.719 | INFO     | b61e06c12fb74ca38e54f5e2cf29e4dd | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-25 08:40:16.720 | INFO     | b61e06c12fb74ca38e54f5e2cf29e4dd | 文件上传完成: test.pdf, 大小: 96853 bytes
2025-08-25 08:40:16.998 | INFO     | b61e06c12fb74ca38e54f5e2cf29e4dd | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-25 08:40:16.999 | INFO     | b61e06c12fb74ca38e54f5e2cf29e4dd | RAGFlow上传API响应: {'code': 0, 'data': [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': '11f978c0814c11f0867288f4da8e1b91', 'location': 'test(1).pdf', 'name': 'test(1).pdf', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 96853, 'thumbnail': 'thumbnail_11f978c0814c11f0867288f4da8e1b91.png', 'type': 'pdf'}]}
2025-08-25 08:40:17.000 | INFO     | b61e06c12fb74ca38e54f5e2cf29e4dd | 响应data类型: <class 'list'>
2025-08-25 08:40:17.000 | INFO     | b61e06c12fb74ca38e54f5e2cf29e4dd | 响应data内容: [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': '11f978c0814c11f0867288f4da8e1b91', 'location': 'test(1).pdf', 'name': 'test(1).pdf', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 96853, 'thumbnail': 'thumbnail_11f978c0814c11f0867288f4da8e1b91.png', 'type': 'pdf'}]
2025-08-25 08:40:17.001 | INFO     | b61e06c12fb74ca38e54f5e2cf29e4dd | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 290.509ms
2025-08-25 08:40:17.007 | INFO     | 4e0efa1ccf5b48c4a84b5e51999f7715 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:17.009 | INFO     | 4e0efa1ccf5b48c4a84b5e51999f7715 | 成功认证Java用户: pythontest
2025-08-25 08:40:17.015 | INFO     | 4e0efa1ccf5b48c4a84b5e51999f7715 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:17.016 | INFO     | 4e0efa1ccf5b48c4a84b5e51999f7715 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-25 08:40:17.016 | INFO     | 4e0efa1ccf5b48c4a84b5e51999f7715 | 文件上传完成: test.docx, 大小: 21409 bytes
2025-08-25 08:40:17.151 | INFO     | 4e0efa1ccf5b48c4a84b5e51999f7715 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-25 08:40:17.152 | INFO     | 4e0efa1ccf5b48c4a84b5e51999f7715 | RAGFlow上传API响应: {'code': 0, 'data': [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': '122db87b814c11f0b90b88f4da8e1b91', 'location': 'test(1).docx', 'name': 'test(1).docx', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 21409, 'thumbnail': '', 'type': 'doc'}]}
2025-08-25 08:40:17.152 | INFO     | 4e0efa1ccf5b48c4a84b5e51999f7715 | 响应data类型: <class 'list'>
2025-08-25 08:40:17.153 | INFO     | 4e0efa1ccf5b48c4a84b5e51999f7715 | 响应data内容: [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': '122db87b814c11f0b90b88f4da8e1b91', 'location': 'test(1).docx', 'name': 'test(1).docx', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 21409, 'thumbnail': '', 'type': 'doc'}]
2025-08-25 08:40:17.154 | INFO     | 4e0efa1ccf5b48c4a84b5e51999f7715 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 146.785ms
2025-08-25 08:40:17.160 | INFO     | a4cd2fa128274b8fa743ab49dc9847be | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:17.161 | INFO     | a4cd2fa128274b8fa743ab49dc9847be | 成功认证Java用户: pythontest
2025-08-25 08:40:17.178 | INFO     | a4cd2fa128274b8fa743ab49dc9847be | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:17.178 | INFO     | a4cd2fa128274b8fa743ab49dc9847be | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-25 08:40:17.204 | INFO     | a4cd2fa128274b8fa743ab49dc9847be | 文件上传完成: test.xlsx, 大小: 1152626 bytes
2025-08-25 08:40:17.329 | INFO     | a4cd2fa128274b8fa743ab49dc9847be | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-25 08:40:17.330 | INFO     | a4cd2fa128274b8fa743ab49dc9847be | RAGFlow上传API响应: {'code': 0, 'data': [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': '1247d4c5814c11f0ab7b88f4da8e1b91', 'location': 'test(2).xlsx', 'name': 'test(2).xlsx', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 1152626, 'thumbnail': '', 'type': 'doc'}]}
2025-08-25 08:40:17.330 | INFO     | a4cd2fa128274b8fa743ab49dc9847be | 响应data类型: <class 'list'>
2025-08-25 08:40:17.331 | INFO     | a4cd2fa128274b8fa743ab49dc9847be | 响应data内容: [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': '1247d4c5814c11f0ab7b88f4da8e1b91', 'location': 'test(2).xlsx', 'name': 'test(2).xlsx', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 1152626, 'thumbnail': '', 'type': 'doc'}]
2025-08-25 08:40:17.332 | INFO     | a4cd2fa128274b8fa743ab49dc9847be | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 173.013ms
2025-08-25 08:40:17.338 | INFO     | 07ca885e25a34be3aa62b8e423d16d52 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:17.340 | INFO     | 07ca885e25a34be3aa62b8e423d16d52 | 成功认证Java用户: pythontest
2025-08-25 08:40:17.346 | INFO     | 07ca885e25a34be3aa62b8e423d16d52 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:17.346 | INFO     | 07ca885e25a34be3aa62b8e423d16d52 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-25 08:40:17.346 | INFO     | 07ca885e25a34be3aa62b8e423d16d52 | 文件上传完成: 新文件 8 (2).txt, 大小: 338 bytes
2025-08-25 08:40:17.515 | INFO     | 07ca885e25a34be3aa62b8e423d16d52 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-25 08:40:17.516 | INFO     | 07ca885e25a34be3aa62b8e423d16d52 | RAGFlow上传API响应: {'code': 0, 'data': [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': '126026f2814c11f0a9aa88f4da8e1b91', 'location': '新文件 8 (3).txt', 'name': '新文件 8 (3).txt', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 338, 'thumbnail': '', 'type': 'doc'}]}
2025-08-25 08:40:17.517 | INFO     | 07ca885e25a34be3aa62b8e423d16d52 | 响应data类型: <class 'list'>
2025-08-25 08:40:17.517 | INFO     | 07ca885e25a34be3aa62b8e423d16d52 | 响应data内容: [{'chunk_method': 'naive', 'created_by': '3f7c3e77475c11f0b031345a603cb29c', 'dataset_id': 'f1d907257e3311f0954288f4da8e1b91', 'id': '126026f2814c11f0a9aa88f4da8e1b91', 'location': '新文件 8 (3).txt', 'name': '新文件 8 (3).txt', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'raptor': {'use_raptor': False}}, 'run': 'UNSTART', 'size': 338, 'thumbnail': '', 'type': 'doc'}]
2025-08-25 08:40:17.518 | INFO     | 07ca885e25a34be3aa62b8e423d16d52 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 180.065ms
2025-08-25 08:40:17.530 | INFO     | 8af247fe82924f479d25d1a583458555 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:17.532 | INFO     | 8af247fe82924f479d25d1a583458555 | 成功认证Java用户: pythontest
2025-08-25 08:40:17.538 | INFO     | 8af247fe82924f479d25d1a583458555 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:17.539 | INFO     | 8af247fe82924f479d25d1a583458555 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:40:17.559 | INFO     | 8af247fe82924f479d25d1a583458555 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:40:17.561 | INFO     | 8af247fe82924f479d25d1a583458555 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 31.369ms
2025-08-25 08:40:23.524 | INFO     | 9332920e2ca64f2ab882b09b0e598b61 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:23.525 | INFO     | 04eb4db708284bac82433a2a0fb45c90 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:23.526 | INFO     | 7f1214938aab4bcb9448833db32d4062 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:23.528 | INFO     | 9332920e2ca64f2ab882b09b0e598b61 | 成功认证Java用户: pythontest
2025-08-25 08:40:23.531 | INFO     | 04eb4db708284bac82433a2a0fb45c90 | 成功认证Java用户: pythontest
2025-08-25 08:40:23.532 | INFO     | 7f1214938aab4bcb9448833db32d4062 | 成功认证Java用户: pythontest
2025-08-25 08:40:23.542 | INFO     | 9332920e2ca64f2ab882b09b0e598b61 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:23.543 | INFO     | 9332920e2ca64f2ab882b09b0e598b61 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:40:23.543 | INFO     | 9332920e2ca64f2ab882b09b0e598b61 | 使用文档更新API重置文档 126026f2814c11f0a9aa88f4da8e1b91 的状态
2025-08-25 08:40:23.554 | INFO     | 7f1214938aab4bcb9448833db32d4062 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:23.554 | INFO     | 7f1214938aab4bcb9448833db32d4062 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:40:23.555 | INFO     | 7f1214938aab4bcb9448833db32d4062 | 使用文档更新API重置文档 122db87b814c11f0b90b88f4da8e1b91 的状态
2025-08-25 08:40:23.558 | INFO     | 04eb4db708284bac82433a2a0fb45c90 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:23.560 | INFO     | 04eb4db708284bac82433a2a0fb45c90 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:40:23.560 | INFO     | 04eb4db708284bac82433a2a0fb45c90 | 使用文档更新API重置文档 1247d4c5814c11f0ab7b88f4da8e1b91 的状态
2025-08-25 08:40:23.563 | INFO     | 9332920e2ca64f2ab882b09b0e598b61 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/126026f2814c11f0a9aa88f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:40:23.564 | INFO     | 9332920e2ca64f2ab882b09b0e598b61 | 成功重置文档 126026f2814c11f0a9aa88f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:40:23.565 | INFO     | 9332920e2ca64f2ab882b09b0e598b61 | 开始解析文档: 126026f2814c11f0a9aa88f4da8e1b91
2025-08-25 08:40:23.606 | INFO     | 7f1214938aab4bcb9448833db32d4062 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/122db87b814c11f0b90b88f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:40:23.607 | INFO     | 7f1214938aab4bcb9448833db32d4062 | 成功重置文档 122db87b814c11f0b90b88f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:40:23.608 | INFO     | 7f1214938aab4bcb9448833db32d4062 | 开始解析文档: 122db87b814c11f0b90b88f4da8e1b91
2025-08-25 08:40:23.610 | INFO     | 04eb4db708284bac82433a2a0fb45c90 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/1247d4c5814c11f0ab7b88f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:40:23.612 | INFO     | 04eb4db708284bac82433a2a0fb45c90 | 成功重置文档 1247d4c5814c11f0ab7b88f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:40:23.612 | INFO     | 04eb4db708284bac82433a2a0fb45c90 | 开始解析文档: 1247d4c5814c11f0ab7b88f4da8e1b91
2025-08-25 08:40:23.681 | INFO     | 9332920e2ca64f2ab882b09b0e598b61 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:40:23.682 | INFO     | 9332920e2ca64f2ab882b09b0e598b61 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/126026f2814c11f0a9aa88f4da8e1b91/parse | 158.754ms
2025-08-25 08:40:23.684 | INFO     | ea18ad9d57c549e9bc0b1a06ad308361 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:23.685 | INFO     | ea18ad9d57c549e9bc0b1a06ad308361 | 成功认证Java用户: pythontest
2025-08-25 08:40:23.692 | INFO     | ea18ad9d57c549e9bc0b1a06ad308361 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:23.692 | INFO     | ea18ad9d57c549e9bc0b1a06ad308361 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-25 08:40:23.692 | INFO     | ea18ad9d57c549e9bc0b1a06ad308361 | 使用文档更新API重置文档 11f978c0814c11f0867288f4da8e1b91 的状态
2025-08-25 08:40:23.709 | INFO     | ea18ad9d57c549e9bc0b1a06ad308361 | HTTP Request: PUT http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/11f978c0814c11f0867288f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 08:40:23.710 | INFO     | ea18ad9d57c549e9bc0b1a06ad308361 | 成功重置文档 11f978c0814c11f0867288f4da8e1b91 的状态: {'code': 0}
2025-08-25 08:40:23.710 | INFO     | ea18ad9d57c549e9bc0b1a06ad308361 | 开始解析文档: 11f978c0814c11f0867288f4da8e1b91
2025-08-25 08:40:23.713 | INFO     | 04eb4db708284bac82433a2a0fb45c90 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:40:23.715 | INFO     | 04eb4db708284bac82433a2a0fb45c90 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/1247d4c5814c11f0ab7b88f4da8e1b91/parse | 190.609ms
2025-08-25 08:40:23.755 | INFO     | 7f1214938aab4bcb9448833db32d4062 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:40:23.756 | INFO     | 7f1214938aab4bcb9448833db32d4062 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/122db87b814c11f0b90b88f4da8e1b91/parse | 230.130ms
2025-08-25 08:40:23.845 | INFO     | ea18ad9d57c549e9bc0b1a06ad308361 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/chunks "HTTP/1.1 200 OK"
2025-08-25 08:40:23.847 | INFO     | ea18ad9d57c549e9bc0b1a06ad308361 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/11f978c0814c11f0867288f4da8e1b91/parse | 162.226ms
2025-08-25 08:40:23.851 | INFO     | a98a36e757d947e0bed5f11d4f123cdd | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:23.853 | INFO     | a98a36e757d947e0bed5f11d4f123cdd | 成功认证Java用户: pythontest
2025-08-25 08:40:23.858 | INFO     | a98a36e757d947e0bed5f11d4f123cdd | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:23.859 | INFO     | a98a36e757d947e0bed5f11d4f123cdd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:40:23.875 | INFO     | a98a36e757d947e0bed5f11d4f123cdd | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:40:23.877 | INFO     | a98a36e757d947e0bed5f11d4f123cdd | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 25.944ms
2025-08-25 08:40:34.442 | INFO     | 60b6133737d04a4e8905b42537caa450 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:34.444 | INFO     | 60b6133737d04a4e8905b42537caa450 | 成功认证Java用户: pythontest
2025-08-25 08:40:34.448 | INFO     | 60b6133737d04a4e8905b42537caa450 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:34.448 | INFO     | 60b6133737d04a4e8905b42537caa450 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:40:34.460 | INFO     | 60b6133737d04a4e8905b42537caa450 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:40:34.461 | INFO     | 60b6133737d04a4e8905b42537caa450 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 19.149ms
2025-08-25 08:40:44.437 | INFO     | c801f05f038d4fad923beeb86ed310c3 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:44.438 | INFO     | c801f05f038d4fad923beeb86ed310c3 | 成功认证Java用户: pythontest
2025-08-25 08:40:44.444 | INFO     | c801f05f038d4fad923beeb86ed310c3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:44.444 | INFO     | c801f05f038d4fad923beeb86ed310c3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:40:44.456 | INFO     | c801f05f038d4fad923beeb86ed310c3 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:40:44.457 | INFO     | c801f05f038d4fad923beeb86ed310c3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 20.485ms
2025-08-25 08:40:54.447 | INFO     | 837ecf6d00454c9db1f37871b251099e | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:40:54.448 | INFO     | 837ecf6d00454c9db1f37871b251099e | 成功认证Java用户: pythontest
2025-08-25 08:40:54.453 | INFO     | 837ecf6d00454c9db1f37871b251099e | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:40:54.453 | INFO     | 837ecf6d00454c9db1f37871b251099e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:40:54.470 | INFO     | 837ecf6d00454c9db1f37871b251099e | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:40:54.470 | INFO     | 837ecf6d00454c9db1f37871b251099e | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 24.009ms
2025-08-25 08:41:06.151 | INFO     | 2a92b6791a4b4177aeddf2b6104b5fed | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:41:06.152 | INFO     | 2a92b6791a4b4177aeddf2b6104b5fed | 成功认证Java用户: pythontest
2025-08-25 08:41:06.157 | INFO     | 2a92b6791a4b4177aeddf2b6104b5fed | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:41:06.157 | INFO     | 2a92b6791a4b4177aeddf2b6104b5fed | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:41:06.169 | INFO     | 2a92b6791a4b4177aeddf2b6104b5fed | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:41:06.169 | INFO     | 2a92b6791a4b4177aeddf2b6104b5fed | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 18.672ms
2025-08-25 08:41:06.662 | INFO     | 1cfdc54dd2d94d3cb6f1bd0815837194 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:41:06.663 | INFO     | 1cfdc54dd2d94d3cb6f1bd0815837194 | 成功认证Java用户: pythontest
2025-08-25 08:41:06.667 | INFO     | 1cfdc54dd2d94d3cb6f1bd0815837194 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:41:06.668 | INFO     | 1cfdc54dd2d94d3cb6f1bd0815837194 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:41:06.680 | INFO     | 1cfdc54dd2d94d3cb6f1bd0815837194 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:41:06.681 | INFO     | 1cfdc54dd2d94d3cb6f1bd0815837194 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 18.625ms
2025-08-25 08:41:07.392 | INFO     | 00a833f588a4451fb24077440ba57a3f | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:41:07.393 | INFO     | 00a833f588a4451fb24077440ba57a3f | 成功认证Java用户: pythontest
2025-08-25 08:41:07.397 | INFO     | 00a833f588a4451fb24077440ba57a3f | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:41:07.398 | INFO     | 00a833f588a4451fb24077440ba57a3f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:41:07.415 | INFO     | 00a833f588a4451fb24077440ba57a3f | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:41:07.417 | INFO     | 00a833f588a4451fb24077440ba57a3f | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 24.772ms
2025-08-25 08:41:07.928 | INFO     | be07ea7488674284ba588f3d0f6e0c0a | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:41:07.929 | INFO     | be07ea7488674284ba588f3d0f6e0c0a | 成功认证Java用户: pythontest
2025-08-25 08:41:07.933 | INFO     | be07ea7488674284ba588f3d0f6e0c0a | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:41:07.933 | INFO     | be07ea7488674284ba588f3d0f6e0c0a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:41:07.945 | INFO     | be07ea7488674284ba588f3d0f6e0c0a | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:41:07.946 | INFO     | be07ea7488674284ba588f3d0f6e0c0a | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 18.129ms
2025-08-25 08:41:08.769 | INFO     | 53c49b57cb0a42a68a48ee79f3e91ee7 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:41:08.770 | INFO     | 53c49b57cb0a42a68a48ee79f3e91ee7 | 成功认证Java用户: pythontest
2025-08-25 08:41:08.776 | INFO     | 53c49b57cb0a42a68a48ee79f3e91ee7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:41:08.777 | INFO     | 53c49b57cb0a42a68a48ee79f3e91ee7 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:41:08.794 | INFO     | 53c49b57cb0a42a68a48ee79f3e91ee7 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:41:08.796 | INFO     | 53c49b57cb0a42a68a48ee79f3e91ee7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 27.565ms
2025-08-25 08:41:15.478 | INFO     | 4a9b3866a3b2473dbf07d144efc99327 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:41:15.479 | INFO     | 4a9b3866a3b2473dbf07d144efc99327 | 成功认证Java用户: pythontest
2025-08-25 08:41:15.484 | INFO     | 4a9b3866a3b2473dbf07d144efc99327 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:41:15.484 | INFO     | 4a9b3866a3b2473dbf07d144efc99327 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:41:15.500 | INFO     | 4a9b3866a3b2473dbf07d144efc99327 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:41:15.502 | INFO     | 4a9b3866a3b2473dbf07d144efc99327 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 24.088ms
2025-08-25 08:41:15.517 | INFO     | c0fbf6fc46cf427a97eaf4fbc29be648 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:41:15.518 | INFO     | c0fbf6fc46cf427a97eaf4fbc29be648 | 成功认证Java用户: pythontest
2025-08-25 08:41:15.524 | INFO     | c0fbf6fc46cf427a97eaf4fbc29be648 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:41:15.524 | INFO     | c0fbf6fc46cf427a97eaf4fbc29be648 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:41:15.541 | INFO     | c0fbf6fc46cf427a97eaf4fbc29be648 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:41:15.542 | INFO     | c0fbf6fc46cf427a97eaf4fbc29be648 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 24.820ms
2025-08-25 08:44:50.354 | INFO     | 5eb5bcb04402414598cad51b44af9530 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:44:50.356 | INFO     | 5eb5bcb04402414598cad51b44af9530 | 成功认证Java用户: pythontest
2025-08-25 08:44:50.359 | INFO     | 5eb5bcb04402414598cad51b44af9530 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:44:50.360 | INFO     | 5eb5bcb04402414598cad51b44af9530 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:44:50.371 | INFO     | 5eb5bcb04402414598cad51b44af9530 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:44:50.372 | INFO     | 5eb5bcb04402414598cad51b44af9530 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 17.373ms
2025-08-25 08:44:50.400 | INFO     | dfe0b4a21a9f47568b9f82b6db56e717 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:44:50.401 | INFO     | dfe0b4a21a9f47568b9f82b6db56e717 | 成功认证Java用户: pythontest
2025-08-25 08:44:50.405 | INFO     | dfe0b4a21a9f47568b9f82b6db56e717 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:44:50.405 | INFO     | dfe0b4a21a9f47568b9f82b6db56e717 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:44:50.423 | INFO     | dfe0b4a21a9f47568b9f82b6db56e717 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:44:50.423 | INFO     | dfe0b4a21a9f47568b9f82b6db56e717 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 23.332ms
2025-08-25 08:45:06.607 | INFO     | d8408be8bc5542deb60eee68f8a02fe5 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:06.608 | INFO     | d8408be8bc5542deb60eee68f8a02fe5 | 成功认证Java用户: pythontest
2025-08-25 08:45:06.613 | INFO     | d8408be8bc5542deb60eee68f8a02fe5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:06.614 | INFO     | d8408be8bc5542deb60eee68f8a02fe5 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:06.634 | INFO     | d8408be8bc5542deb60eee68f8a02fe5 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:06.635 | INFO     | d8408be8bc5542deb60eee68f8a02fe5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 28.907ms
2025-08-25 08:45:06.652 | INFO     | c0c2bdf6ca304954ac6de81b48fad514 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:06.653 | INFO     | c0c2bdf6ca304954ac6de81b48fad514 | 成功认证Java用户: pythontest
2025-08-25 08:45:06.658 | INFO     | c0c2bdf6ca304954ac6de81b48fad514 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:06.659 | INFO     | c0c2bdf6ca304954ac6de81b48fad514 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:06.680 | INFO     | c0c2bdf6ca304954ac6de81b48fad514 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:06.682 | INFO     | c0c2bdf6ca304954ac6de81b48fad514 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 29.565ms
2025-08-25 08:45:06.849 | INFO     | 03e0a313fbb84294bc7ce0c54d6e2692 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:06.851 | INFO     | 03e0a313fbb84294bc7ce0c54d6e2692 | 成功认证Java用户: pythontest
2025-08-25 08:45:06.856 | INFO     | 03e0a313fbb84294bc7ce0c54d6e2692 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:06.856 | INFO     | 03e0a313fbb84294bc7ce0c54d6e2692 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:06.870 | INFO     | 03e0a313fbb84294bc7ce0c54d6e2692 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:06.871 | INFO     | 03e0a313fbb84294bc7ce0c54d6e2692 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 21.131ms
2025-08-25 08:45:06.928 | INFO     | a281aead98044e81bc0dd14c025c35af | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:06.929 | INFO     | a281aead98044e81bc0dd14c025c35af | 成功认证Java用户: pythontest
2025-08-25 08:45:06.933 | INFO     | a281aead98044e81bc0dd14c025c35af | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:06.933 | INFO     | a281aead98044e81bc0dd14c025c35af | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:06.946 | INFO     | a281aead98044e81bc0dd14c025c35af | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:06.947 | INFO     | a281aead98044e81bc0dd14c025c35af | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 19.703ms
2025-08-25 08:45:08.209 | INFO     | 898e0d4b5c0c43d6ae85e582e307b067 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:08.210 | INFO     | 898e0d4b5c0c43d6ae85e582e307b067 | 成功认证Java用户: pythontest
2025-08-25 08:45:08.214 | INFO     | 898e0d4b5c0c43d6ae85e582e307b067 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:08.214 | INFO     | 898e0d4b5c0c43d6ae85e582e307b067 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:08.226 | INFO     | 898e0d4b5c0c43d6ae85e582e307b067 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:08.227 | INFO     | 898e0d4b5c0c43d6ae85e582e307b067 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 17.712ms
2025-08-25 08:45:08.241 | INFO     | 262d8e1120444c8f9fbd55ec8d023cde | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:08.242 | INFO     | 262d8e1120444c8f9fbd55ec8d023cde | 成功认证Java用户: pythontest
2025-08-25 08:45:08.247 | INFO     | 262d8e1120444c8f9fbd55ec8d023cde | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:08.247 | INFO     | 262d8e1120444c8f9fbd55ec8d023cde | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:08.261 | INFO     | 262d8e1120444c8f9fbd55ec8d023cde | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:08.262 | INFO     | 262d8e1120444c8f9fbd55ec8d023cde | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 21.186ms
2025-08-25 08:45:11.079 | INFO     | 5a0fbac3de964d8f899ed6c57e0ef233 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:11.080 | INFO     | 5a0fbac3de964d8f899ed6c57e0ef233 | 成功认证Java用户: pythontest
2025-08-25 08:45:11.084 | INFO     | 5a0fbac3de964d8f899ed6c57e0ef233 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:11.084 | INFO     | 5a0fbac3de964d8f899ed6c57e0ef233 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:11.096 | INFO     | 5a0fbac3de964d8f899ed6c57e0ef233 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:11.096 | INFO     | 5a0fbac3de964d8f899ed6c57e0ef233 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 17.376ms
2025-08-25 08:45:11.104 | INFO     | 338bc55f94a2499e84d590afce63288e | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:11.105 | INFO     | 338bc55f94a2499e84d590afce63288e | 成功认证Java用户: pythontest
2025-08-25 08:45:11.109 | INFO     | 338bc55f94a2499e84d590afce63288e | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:11.109 | INFO     | 338bc55f94a2499e84d590afce63288e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:11.122 | INFO     | 338bc55f94a2499e84d590afce63288e | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:11.123 | INFO     | 338bc55f94a2499e84d590afce63288e | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 19.682ms
2025-08-25 08:45:12.481 | INFO     | 3f67e6e121f740629861fa042669b0dd | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:12.482 | INFO     | 3f67e6e121f740629861fa042669b0dd | 成功认证Java用户: pythontest
2025-08-25 08:45:12.487 | INFO     | 3f67e6e121f740629861fa042669b0dd | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:12.487 | INFO     | 3f67e6e121f740629861fa042669b0dd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:12.498 | INFO     | 3f67e6e121f740629861fa042669b0dd | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:12.499 | INFO     | 3f67e6e121f740629861fa042669b0dd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 17.914ms
2025-08-25 08:45:12.512 | INFO     | d01adbb275f04d4f8c2ef3135bd72730 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:12.514 | INFO     | d01adbb275f04d4f8c2ef3135bd72730 | 成功认证Java用户: pythontest
2025-08-25 08:45:12.518 | INFO     | d01adbb275f04d4f8c2ef3135bd72730 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:12.518 | INFO     | d01adbb275f04d4f8c2ef3135bd72730 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:12.532 | INFO     | d01adbb275f04d4f8c2ef3135bd72730 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:12.534 | INFO     | d01adbb275f04d4f8c2ef3135bd72730 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 21.881ms
2025-08-25 08:45:16.018 | INFO     | 4588490099af4340be2249e97d5a49e5 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:16.019 | INFO     | 4588490099af4340be2249e97d5a49e5 | 成功认证Java用户: pythontest
2025-08-25 08:45:16.023 | INFO     | 4588490099af4340be2249e97d5a49e5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:16.024 | INFO     | 4588490099af4340be2249e97d5a49e5 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:16.035 | INFO     | 4588490099af4340be2249e97d5a49e5 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:16.036 | INFO     | 4588490099af4340be2249e97d5a49e5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 17.898ms
2025-08-25 08:45:16.050 | INFO     | f45dd4331ae146ddb1b38874f11a5bd1 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:16.051 | INFO     | f45dd4331ae146ddb1b38874f11a5bd1 | 成功认证Java用户: pythontest
2025-08-25 08:45:16.056 | INFO     | f45dd4331ae146ddb1b38874f11a5bd1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:16.057 | INFO     | f45dd4331ae146ddb1b38874f11a5bd1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:16.069 | INFO     | f45dd4331ae146ddb1b38874f11a5bd1 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:16.070 | INFO     | f45dd4331ae146ddb1b38874f11a5bd1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 21.404ms
2025-08-25 08:45:48.674 | INFO     | 6de388cd88844026a181b5ae8734a2e3 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:48.675 | INFO     | 6de388cd88844026a181b5ae8734a2e3 | 成功认证Java用户: pythontest
2025-08-25 08:45:48.679 | INFO     | 6de388cd88844026a181b5ae8734a2e3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:48.679 | INFO     | 6de388cd88844026a181b5ae8734a2e3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:48.691 | INFO     | 6de388cd88844026a181b5ae8734a2e3 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:48.692 | INFO     | 6de388cd88844026a181b5ae8734a2e3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 17.413ms
2025-08-25 08:45:48.710 | INFO     | bc27c4bd903144abaec0867f7a1d3463 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:48.711 | INFO     | bc27c4bd903144abaec0867f7a1d3463 | 成功认证Java用户: pythontest
2025-08-25 08:45:48.716 | INFO     | bc27c4bd903144abaec0867f7a1d3463 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:48.716 | INFO     | bc27c4bd903144abaec0867f7a1d3463 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:48.729 | INFO     | bc27c4bd903144abaec0867f7a1d3463 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:48.730 | INFO     | bc27c4bd903144abaec0867f7a1d3463 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 19.814ms
2025-08-25 08:45:58.215 | INFO     | abefc830590e46669102599753ff7e16 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:58.217 | INFO     | abefc830590e46669102599753ff7e16 | 成功认证Java用户: pythontest
2025-08-25 08:45:58.235 | INFO     | abefc830590e46669102599753ff7e16 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-25 08:45:58.237 | INFO     | abefc830590e46669102599753ff7e16 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.154ms
2025-08-25 08:45:58.239 | INFO     | 4ffea8ce34b14c1796da8f7c1508d5ac | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:58.241 | INFO     | 4ffea8ce34b14c1796da8f7c1508d5ac | 成功认证Java用户: pythontest
2025-08-25 08:45:58.247 | INFO     | 4ffea8ce34b14c1796da8f7c1508d5ac | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:58.247 | INFO     | 4ffea8ce34b14c1796da8f7c1508d5ac | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:45:58.262 | INFO     | 4ffea8ce34b14c1796da8f7c1508d5ac | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:58.264 | INFO     | 4ffea8ce34b14c1796da8f7c1508d5ac | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 24.910ms
2025-08-25 08:45:58.266 | INFO     | c53b1204c4cb494ca44d0d7985fdb805 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:45:58.267 | INFO     | c53b1204c4cb494ca44d0d7985fdb805 | 成功认证Java用户: pythontest
2025-08-25 08:45:58.272 | INFO     | c53b1204c4cb494ca44d0d7985fdb805 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:45:58.273 | INFO     | c53b1204c4cb494ca44d0d7985fdb805 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-25 08:45:58.289 | INFO     | c53b1204c4cb494ca44d0d7985fdb805 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:45:58.291 | INFO     | c53b1204c4cb494ca44d0d7985fdb805 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.967ms
2025-08-25 08:46:45.128 | INFO     | 6966bb134a0e4fa69401c9f1f0979a3c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:46:45.130 | INFO     | 6966bb134a0e4fa69401c9f1f0979a3c | 成功认证Java用户: pythontest
2025-08-25 08:46:45.134 | INFO     | 6966bb134a0e4fa69401c9f1f0979a3c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:46:45.134 | INFO     | 6966bb134a0e4fa69401c9f1f0979a3c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:46:45.146 | INFO     | 6966bb134a0e4fa69401c9f1f0979a3c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:46:45.147 | INFO     | 6966bb134a0e4fa69401c9f1f0979a3c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 19.282ms
2025-08-25 08:46:45.165 | INFO     | 8c5a439f2a584f3e9ac1141074013666 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:46:45.166 | INFO     | 8c5a439f2a584f3e9ac1141074013666 | 成功认证Java用户: pythontest
2025-08-25 08:46:45.171 | INFO     | 8c5a439f2a584f3e9ac1141074013666 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:46:45.171 | INFO     | 8c5a439f2a584f3e9ac1141074013666 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:46:45.189 | INFO     | 8c5a439f2a584f3e9ac1141074013666 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:46:45.190 | INFO     | 8c5a439f2a584f3e9ac1141074013666 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 25.231ms
2025-08-25 08:46:46.287 | INFO     | 1644b31b4efa4e2e84985af1f981aa18 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:46:46.288 | INFO     | 1644b31b4efa4e2e84985af1f981aa18 | 成功认证Java用户: pythontest
2025-08-25 08:46:46.293 | INFO     | 1644b31b4efa4e2e84985af1f981aa18 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:46:46.294 | INFO     | 1644b31b4efa4e2e84985af1f981aa18 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:46:46.309 | INFO     | 1644b31b4efa4e2e84985af1f981aa18 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:46:46.310 | INFO     | 1644b31b4efa4e2e84985af1f981aa18 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 23.842ms
2025-08-25 08:46:46.320 | INFO     | d5c47c1af0764a7295001ca5218f6481 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:46:46.321 | INFO     | d5c47c1af0764a7295001ca5218f6481 | 成功认证Java用户: pythontest
2025-08-25 08:46:46.334 | INFO     | d5c47c1af0764a7295001ca5218f6481 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:46:46.335 | INFO     | d5c47c1af0764a7295001ca5218f6481 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:46:46.357 | INFO     | d5c47c1af0764a7295001ca5218f6481 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:46:46.358 | INFO     | d5c47c1af0764a7295001ca5218f6481 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 38.979ms
2025-08-25 08:46:48.451 | INFO     | 38bdc025b2174b7c8ba45d7384ec45e4 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:46:48.452 | INFO     | c0354cc666584419b3f46ee95c170396 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:46:48.453 | INFO     | 234d01908b3440bfae373b717b116291 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:46:48.454 | INFO     | 38bdc025b2174b7c8ba45d7384ec45e4 | 成功认证Java用户: pythontest
2025-08-25 08:46:48.455 | INFO     | 234d01908b3440bfae373b717b116291 | 成功认证Java用户: pythontest
2025-08-25 08:46:48.455 | INFO     | c0354cc666584419b3f46ee95c170396 | 成功认证Java用户: pythontest
2025-08-25 08:46:48.464 | INFO     | 234d01908b3440bfae373b717b116291 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:46:48.464 | INFO     | 234d01908b3440bfae373b717b116291 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-25 08:46:48.470 | INFO     | c0354cc666584419b3f46ee95c170396 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:46:48.470 | INFO     | c0354cc666584419b3f46ee95c170396 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:46:48.474 | INFO     | 38bdc025b2174b7c8ba45d7384ec45e4 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-25 08:46:48.476 | INFO     | 38bdc025b2174b7c8ba45d7384ec45e4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 25.751ms
2025-08-25 08:46:48.484 | INFO     | 234d01908b3440bfae373b717b116291 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:46:48.486 | INFO     | 234d01908b3440bfae373b717b116291 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 33.416ms
2025-08-25 08:46:48.490 | INFO     | c0354cc666584419b3f46ee95c170396 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:46:48.492 | INFO     | c0354cc666584419b3f46ee95c170396 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 41.205ms
2025-08-25 08:46:48.887 | INFO     | 9d715e117e4f40e5ac836bbbf7e7f6bb | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:46:48.889 | INFO     | 9d715e117e4f40e5ac836bbbf7e7f6bb | 成功认证Java用户: pythontest
2025-08-25 08:46:48.894 | INFO     | 9d715e117e4f40e5ac836bbbf7e7f6bb | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:46:48.894 | INFO     | 9d715e117e4f40e5ac836bbbf7e7f6bb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:46:48.910 | INFO     | 9d715e117e4f40e5ac836bbbf7e7f6bb | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:46:48.911 | INFO     | 9d715e117e4f40e5ac836bbbf7e7f6bb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 24.290ms
2025-08-25 08:46:48.951 | INFO     | ce02dce47260488c9c64ac04813c6710 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:46:48.953 | INFO     | ce02dce47260488c9c64ac04813c6710 | 成功认证Java用户: pythontest
2025-08-25 08:46:48.959 | INFO     | ce02dce47260488c9c64ac04813c6710 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:46:48.959 | INFO     | ce02dce47260488c9c64ac04813c6710 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:46:49.006 | INFO     | ce02dce47260488c9c64ac04813c6710 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:46:49.008 | INFO     | ce02dce47260488c9c64ac04813c6710 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 57.071ms
2025-08-25 08:46:49.022 | INFO     | 58e536bcf6804289a2332f65c087f279 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:46:49.023 | INFO     | 58e536bcf6804289a2332f65c087f279 | 成功认证Java用户: pythontest
2025-08-25 08:46:49.028 | INFO     | 58e536bcf6804289a2332f65c087f279 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:46:49.029 | INFO     | 58e536bcf6804289a2332f65c087f279 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:46:49.046 | INFO     | 58e536bcf6804289a2332f65c087f279 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:46:49.048 | INFO     | 58e536bcf6804289a2332f65c087f279 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 26.545ms
2025-08-25 08:48:05.397 | INFO     | b9aa56d1050d4a0b85348cd140e2ecb3 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:48:05.400 | INFO     | b9aa56d1050d4a0b85348cd140e2ecb3 | 成功认证Java用户: pythontest
2025-08-25 08:48:05.404 | INFO     | b9aa56d1050d4a0b85348cd140e2ecb3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:48:05.404 | INFO     | b9aa56d1050d4a0b85348cd140e2ecb3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:48:05.416 | INFO     | b9aa56d1050d4a0b85348cd140e2ecb3 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:48:05.417 | INFO     | b9aa56d1050d4a0b85348cd140e2ecb3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 18.491ms
2025-08-25 08:48:05.435 | INFO     | 7b6307f6bdcb448385e546a85d8102f9 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:48:05.436 | INFO     | 7b6307f6bdcb448385e546a85d8102f9 | 成功认证Java用户: pythontest
2025-08-25 08:48:05.441 | INFO     | 7b6307f6bdcb448385e546a85d8102f9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:48:05.441 | INFO     | 7b6307f6bdcb448385e546a85d8102f9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:48:05.454 | INFO     | 7b6307f6bdcb448385e546a85d8102f9 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:48:05.455 | INFO     | 7b6307f6bdcb448385e546a85d8102f9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 20.195ms
2025-08-25 08:48:37.444 | INFO     | a1c327c4ef3f4f39840a162bfd58f90f | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:48:37.446 | INFO     | a1c327c4ef3f4f39840a162bfd58f90f | 成功认证Java用户: pythontest
2025-08-25 08:48:37.457 | INFO     | a1c327c4ef3f4f39840a162bfd58f90f | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-25 08:48:37.458 | INFO     | a1c327c4ef3f4f39840a162bfd58f90f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 13.134ms
2025-08-25 08:48:37.459 | INFO     | 4492c577200b4993baf972785cba1a32 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:48:37.460 | INFO     | 4492c577200b4993baf972785cba1a32 | 成功认证Java用户: pythontest
2025-08-25 08:48:37.464 | INFO     | 4492c577200b4993baf972785cba1a32 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:48:37.465 | INFO     | 4492c577200b4993baf972785cba1a32 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:48:37.476 | INFO     | 4492c577200b4993baf972785cba1a32 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:48:37.477 | INFO     | 4492c577200b4993baf972785cba1a32 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 17.591ms
2025-08-25 08:48:37.479 | INFO     | b1a42088c22a422a8241ea50f9b9d87d | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:48:37.480 | INFO     | b1a42088c22a422a8241ea50f9b9d87d | 成功认证Java用户: pythontest
2025-08-25 08:48:37.482 | INFO     | b1a42088c22a422a8241ea50f9b9d87d | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:48:37.483 | INFO     | b1a42088c22a422a8241ea50f9b9d87d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-25 08:48:37.493 | INFO     | b1a42088c22a422a8241ea50f9b9d87d | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:48:37.494 | INFO     | b1a42088c22a422a8241ea50f9b9d87d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 15.783ms
2025-08-25 08:48:38.933 | INFO     | db389be3d54346e1a10d039bc6cd68f3 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:48:38.934 | INFO     | db389be3d54346e1a10d039bc6cd68f3 | 成功认证Java用户: pythontest
2025-08-25 08:48:38.938 | INFO     | db389be3d54346e1a10d039bc6cd68f3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:48:38.939 | INFO     | db389be3d54346e1a10d039bc6cd68f3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:48:38.950 | INFO     | db389be3d54346e1a10d039bc6cd68f3 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:48:38.952 | INFO     | db389be3d54346e1a10d039bc6cd68f3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 18.167ms
2025-08-25 08:48:38.964 | INFO     | 99cf2bfff53d4cc682e5ee6a7510e9a6 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:48:38.965 | INFO     | 99cf2bfff53d4cc682e5ee6a7510e9a6 | 成功认证Java用户: pythontest
2025-08-25 08:48:38.970 | INFO     | 99cf2bfff53d4cc682e5ee6a7510e9a6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:48:38.970 | INFO     | 99cf2bfff53d4cc682e5ee6a7510e9a6 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:48:38.995 | INFO     | 99cf2bfff53d4cc682e5ee6a7510e9a6 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:48:38.996 | INFO     | 99cf2bfff53d4cc682e5ee6a7510e9a6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 32.762ms
2025-08-25 08:50:52.707 | INFO     | 8265e9faeda641e4b63ffb510e822ed5 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:50:52.708 | INFO     | 8265e9faeda641e4b63ffb510e822ed5 | 成功认证Java用户: pythontest
2025-08-25 08:50:52.712 | INFO     | 8265e9faeda641e4b63ffb510e822ed5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:50:52.712 | INFO     | 8265e9faeda641e4b63ffb510e822ed5 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:50:52.723 | INFO     | 8265e9faeda641e4b63ffb510e822ed5 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:50:52.724 | INFO     | 8265e9faeda641e4b63ffb510e822ed5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 17.238ms
2025-08-25 08:50:52.740 | INFO     | 200fa72f36564e0691f8e8160e9d0586 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:50:52.740 | INFO     | 200fa72f36564e0691f8e8160e9d0586 | 成功认证Java用户: pythontest
2025-08-25 08:50:52.744 | INFO     | 200fa72f36564e0691f8e8160e9d0586 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:50:52.744 | INFO     | 200fa72f36564e0691f8e8160e9d0586 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:50:52.756 | INFO     | 200fa72f36564e0691f8e8160e9d0586 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:50:52.757 | INFO     | 200fa72f36564e0691f8e8160e9d0586 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 17.946ms
2025-08-25 08:52:11.703 | INFO     | 23f4d516c7af43aca89222a66957ba2c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:52:11.704 | INFO     | 23f4d516c7af43aca89222a66957ba2c | 成功认证Java用户: pythontest
2025-08-25 08:52:11.709 | INFO     | 23f4d516c7af43aca89222a66957ba2c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:52:11.709 | INFO     | 23f4d516c7af43aca89222a66957ba2c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:52:11.721 | INFO     | 23f4d516c7af43aca89222a66957ba2c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:52:11.721 | INFO     | 23f4d516c7af43aca89222a66957ba2c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 18.099ms
2025-08-25 08:52:11.730 | INFO     | 2d1ff1843d5642198b94fb9afdb27e2b | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:52:11.731 | INFO     | 2d1ff1843d5642198b94fb9afdb27e2b | 成功认证Java用户: pythontest
2025-08-25 08:52:11.736 | INFO     | 2d1ff1843d5642198b94fb9afdb27e2b | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:52:11.736 | INFO     | 2d1ff1843d5642198b94fb9afdb27e2b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:52:11.748 | INFO     | 2d1ff1843d5642198b94fb9afdb27e2b | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:52:11.749 | INFO     | 2d1ff1843d5642198b94fb9afdb27e2b | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 18.796ms
2025-08-25 08:52:13.643 | INFO     | 7e8f5e7c49034d069c5f9cadd6595ac5 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:52:13.645 | INFO     | 7e8f5e7c49034d069c5f9cadd6595ac5 | 成功认证Java用户: pythontest
2025-08-25 08:52:13.650 | INFO     | 7e8f5e7c49034d069c5f9cadd6595ac5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:52:13.651 | INFO     | 7e8f5e7c49034d069c5f9cadd6595ac5 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:52:13.665 | INFO     | 7e8f5e7c49034d069c5f9cadd6595ac5 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:52:13.666 | INFO     | 7e8f5e7c49034d069c5f9cadd6595ac5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 22.414ms
2025-08-25 08:52:13.681 | INFO     | 77d2e202d7d14c258dceb1e80a7a267a | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:52:13.682 | INFO     | 77d2e202d7d14c258dceb1e80a7a267a | 成功认证Java用户: pythontest
2025-08-25 08:52:13.687 | INFO     | 77d2e202d7d14c258dceb1e80a7a267a | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:52:13.687 | INFO     | 77d2e202d7d14c258dceb1e80a7a267a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:52:13.701 | INFO     | 77d2e202d7d14c258dceb1e80a7a267a | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:52:13.702 | INFO     | 77d2e202d7d14c258dceb1e80a7a267a | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 21.374ms
2025-08-25 08:54:49.132 | INFO     | 8a7a4942e56c452292d0ea7e10f0b007 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:54:49.133 | INFO     | 8a7a4942e56c452292d0ea7e10f0b007 | 成功认证Java用户: pythontest
2025-08-25 08:54:49.137 | INFO     | 8a7a4942e56c452292d0ea7e10f0b007 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:54:49.137 | INFO     | 8a7a4942e56c452292d0ea7e10f0b007 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:54:49.151 | INFO     | 8a7a4942e56c452292d0ea7e10f0b007 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:54:49.152 | INFO     | 8a7a4942e56c452292d0ea7e10f0b007 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 20.590ms
2025-08-25 08:54:49.171 | INFO     | 21363fca87404b45acc4db6cfa352303 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:54:49.172 | INFO     | 21363fca87404b45acc4db6cfa352303 | 成功认证Java用户: pythontest
2025-08-25 08:54:49.177 | INFO     | 21363fca87404b45acc4db6cfa352303 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:54:49.178 | INFO     | 21363fca87404b45acc4db6cfa352303 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:54:49.194 | INFO     | 21363fca87404b45acc4db6cfa352303 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:54:49.196 | INFO     | 21363fca87404b45acc4db6cfa352303 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 26.199ms
2025-08-25 08:57:22.799 | INFO     | e9b0e647e1e94db385e766da6a3464c3 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:57:22.800 | INFO     | e9b0e647e1e94db385e766da6a3464c3 | 成功认证Java用户: pythontest
2025-08-25 08:57:22.804 | INFO     | e9b0e647e1e94db385e766da6a3464c3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:57:22.805 | INFO     | e9b0e647e1e94db385e766da6a3464c3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:57:22.817 | INFO     | e9b0e647e1e94db385e766da6a3464c3 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:57:22.818 | INFO     | e9b0e647e1e94db385e766da6a3464c3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 19.946ms
2025-08-25 08:57:22.838 | INFO     | ead55cc112f745bc97155fce566c94c1 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:57:22.839 | INFO     | ead55cc112f745bc97155fce566c94c1 | 成功认证Java用户: pythontest
2025-08-25 08:57:22.843 | INFO     | ead55cc112f745bc97155fce566c94c1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:57:22.843 | INFO     | ead55cc112f745bc97155fce566c94c1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:57:22.859 | INFO     | ead55cc112f745bc97155fce566c94c1 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:57:22.860 | INFO     | ead55cc112f745bc97155fce566c94c1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 21.105ms
2025-08-25 08:58:42.593 | INFO     | 86ad7a28df23461ea4e10070d1f13ef8 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:58:42.594 | INFO     | 86ad7a28df23461ea4e10070d1f13ef8 | 成功认证Java用户: pythontest
2025-08-25 08:58:42.600 | INFO     | 86ad7a28df23461ea4e10070d1f13ef8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:58:42.600 | INFO     | 86ad7a28df23461ea4e10070d1f13ef8 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:58:42.621 | INFO     | 86ad7a28df23461ea4e10070d1f13ef8 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:58:42.622 | INFO     | 86ad7a28df23461ea4e10070d1f13ef8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 29.704ms
2025-08-25 08:58:42.641 | INFO     | 3bfec0ee63bd4e05859cbbca1cfa91b0 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:58:42.642 | INFO     | 3bfec0ee63bd4e05859cbbca1cfa91b0 | 成功认证Java用户: pythontest
2025-08-25 08:58:42.647 | INFO     | 3bfec0ee63bd4e05859cbbca1cfa91b0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:58:42.647 | INFO     | 3bfec0ee63bd4e05859cbbca1cfa91b0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:58:42.659 | INFO     | 3bfec0ee63bd4e05859cbbca1cfa91b0 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:58:42.660 | INFO     | 3bfec0ee63bd4e05859cbbca1cfa91b0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 19.329ms
2025-08-25 08:58:43.762 | INFO     | b5a898115c4940719f2b9a469bbdd14c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:58:43.763 | INFO     | b5a898115c4940719f2b9a469bbdd14c | 成功认证Java用户: pythontest
2025-08-25 08:58:43.766 | INFO     | b5a898115c4940719f2b9a469bbdd14c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:58:43.767 | INFO     | b5a898115c4940719f2b9a469bbdd14c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:58:43.777 | INFO     | b5a898115c4940719f2b9a469bbdd14c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:58:43.777 | INFO     | b5a898115c4940719f2b9a469bbdd14c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 16.166ms
2025-08-25 08:58:43.786 | INFO     | b5e07222e26549ea8cb7c3e2ec4980ae | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:58:43.787 | INFO     | b5e07222e26549ea8cb7c3e2ec4980ae | 成功认证Java用户: pythontest
2025-08-25 08:58:43.791 | INFO     | b5e07222e26549ea8cb7c3e2ec4980ae | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:58:43.791 | INFO     | b5e07222e26549ea8cb7c3e2ec4980ae | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:58:43.804 | INFO     | b5e07222e26549ea8cb7c3e2ec4980ae | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:58:43.804 | INFO     | b5e07222e26549ea8cb7c3e2ec4980ae | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 19.010ms
2025-08-25 08:58:44.825 | INFO     | 6e3356402174417db87da6b576fcc8cf | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:58:44.826 | INFO     | 822d22e2ae0a4df3b5a7c42fae36f76d | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:58:44.827 | INFO     | 6e3356402174417db87da6b576fcc8cf | 成功认证Java用户: pythontest
2025-08-25 08:58:44.829 | INFO     | 822d22e2ae0a4df3b5a7c42fae36f76d | 成功认证Java用户: pythontest
2025-08-25 08:58:44.838 | INFO     | 822d22e2ae0a4df3b5a7c42fae36f76d | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:58:44.839 | INFO     | 822d22e2ae0a4df3b5a7c42fae36f76d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:58:44.846 | INFO     | 6e3356402174417db87da6b576fcc8cf | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-25 08:58:44.848 | INFO     | 6e3356402174417db87da6b576fcc8cf | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.587ms
2025-08-25 08:58:44.850 | INFO     | 63e21616f6fb412a8abbbfac8220773a | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:58:44.851 | INFO     | 63e21616f6fb412a8abbbfac8220773a | 成功认证Java用户: pythontest
2025-08-25 08:58:44.856 | INFO     | 822d22e2ae0a4df3b5a7c42fae36f76d | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:58:44.858 | INFO     | 63e21616f6fb412a8abbbfac8220773a | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:58:44.859 | INFO     | 63e21616f6fb412a8abbbfac8220773a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-25 08:58:44.861 | INFO     | 822d22e2ae0a4df3b5a7c42fae36f76d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 35.602ms
2025-08-25 08:58:44.876 | INFO     | 63e21616f6fb412a8abbbfac8220773a | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:58:44.877 | INFO     | 63e21616f6fb412a8abbbfac8220773a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 27.223ms
2025-08-25 08:58:45.858 | INFO     | 7a018ae7190f4c72a38e1e710cb7514e | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:58:45.860 | INFO     | 7a018ae7190f4c72a38e1e710cb7514e | 成功认证Java用户: pythontest
2025-08-25 08:58:45.865 | INFO     | 7a018ae7190f4c72a38e1e710cb7514e | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:58:45.865 | INFO     | 7a018ae7190f4c72a38e1e710cb7514e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:58:45.882 | INFO     | 7a018ae7190f4c72a38e1e710cb7514e | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:58:45.884 | INFO     | 7a018ae7190f4c72a38e1e710cb7514e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 25.775ms
2025-08-25 08:58:45.957 | INFO     | 34c341e1e647414496c53d1ea0857255 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:58:45.959 | INFO     | 34c341e1e647414496c53d1ea0857255 | 成功认证Java用户: pythontest
2025-08-25 08:58:45.965 | INFO     | 34c341e1e647414496c53d1ea0857255 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:58:45.966 | INFO     | 34c341e1e647414496c53d1ea0857255 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:58:45.984 | INFO     | 34c341e1e647414496c53d1ea0857255 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:58:45.986 | INFO     | 34c341e1e647414496c53d1ea0857255 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 29.378ms
2025-08-25 08:58:46.586 | INFO     | 69c7274047e4407fb4746e7c1f770043 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:58:46.588 | INFO     | 69c7274047e4407fb4746e7c1f770043 | 成功认证Java用户: pythontest
2025-08-25 08:58:46.593 | INFO     | 69c7274047e4407fb4746e7c1f770043 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:58:46.594 | INFO     | 69c7274047e4407fb4746e7c1f770043 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:58:46.607 | INFO     | 69c7274047e4407fb4746e7c1f770043 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:58:46.608 | INFO     | 69c7274047e4407fb4746e7c1f770043 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 22.944ms
2025-08-25 08:58:46.622 | INFO     | 798921ead1ba412c929e11b56fd9c0c0 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:58:46.624 | INFO     | 798921ead1ba412c929e11b56fd9c0c0 | 成功认证Java用户: pythontest
2025-08-25 08:58:46.630 | INFO     | 798921ead1ba412c929e11b56fd9c0c0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:58:46.631 | INFO     | 798921ead1ba412c929e11b56fd9c0c0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:58:46.649 | INFO     | 798921ead1ba412c929e11b56fd9c0c0 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:58:46.651 | INFO     | 798921ead1ba412c929e11b56fd9c0c0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 29.006ms
2025-08-25 08:59:25.394 | INFO     | 5d63aae456464af6afd9125a688f308c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:59:25.396 | INFO     | 5d63aae456464af6afd9125a688f308c | 成功认证Java用户: pythontest
2025-08-25 08:59:25.400 | INFO     | 5d63aae456464af6afd9125a688f308c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:59:25.401 | INFO     | 5d63aae456464af6afd9125a688f308c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:59:25.422 | INFO     | 5d63aae456464af6afd9125a688f308c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:59:25.423 | INFO     | 5d63aae456464af6afd9125a688f308c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 28.692ms
2025-08-25 08:59:25.438 | INFO     | 546eb0814d9d40839e66ec9b81f05f5c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:59:25.439 | INFO     | 546eb0814d9d40839e66ec9b81f05f5c | 成功认证Java用户: pythontest
2025-08-25 08:59:25.444 | INFO     | 546eb0814d9d40839e66ec9b81f05f5c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:59:25.444 | INFO     | 546eb0814d9d40839e66ec9b81f05f5c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:59:25.461 | INFO     | 546eb0814d9d40839e66ec9b81f05f5c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:59:25.462 | INFO     | 546eb0814d9d40839e66ec9b81f05f5c | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 24.871ms
2025-08-25 08:59:29.129 | INFO     | ce772311281c4b448c517ea5602dcb74 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:59:29.131 | INFO     | ce772311281c4b448c517ea5602dcb74 | 成功认证Java用户: pythontest
2025-08-25 08:59:29.136 | INFO     | ce772311281c4b448c517ea5602dcb74 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:59:29.137 | INFO     | ce772311281c4b448c517ea5602dcb74 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:59:29.162 | INFO     | ce772311281c4b448c517ea5602dcb74 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:59:29.164 | INFO     | ce772311281c4b448c517ea5602dcb74 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 35.426ms
2025-08-25 08:59:29.177 | INFO     | 9a6c1f5813d6405da40bd729977e1797 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:59:29.178 | INFO     | 9a6c1f5813d6405da40bd729977e1797 | 成功认证Java用户: pythontest
2025-08-25 08:59:29.184 | INFO     | 9a6c1f5813d6405da40bd729977e1797 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:59:29.184 | INFO     | 9a6c1f5813d6405da40bd729977e1797 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:59:29.202 | INFO     | 9a6c1f5813d6405da40bd729977e1797 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:59:29.205 | INFO     | 9a6c1f5813d6405da40bd729977e1797 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 27.202ms
2025-08-25 08:59:29.700 | INFO     | b1d0e97f43d14c69823fecef7d616bcc | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:59:29.702 | INFO     | b1d0e97f43d14c69823fecef7d616bcc | 成功认证Java用户: pythontest
2025-08-25 08:59:29.708 | INFO     | b1d0e97f43d14c69823fecef7d616bcc | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:59:29.709 | INFO     | b1d0e97f43d14c69823fecef7d616bcc | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:59:29.725 | INFO     | b1d0e97f43d14c69823fecef7d616bcc | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:59:29.726 | INFO     | b1d0e97f43d14c69823fecef7d616bcc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 26.671ms
2025-08-25 08:59:29.735 | INFO     | 95f4d08b1a51496d816382c3817c733c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:59:29.737 | INFO     | 95f4d08b1a51496d816382c3817c733c | 成功认证Java用户: pythontest
2025-08-25 08:59:29.742 | INFO     | 95f4d08b1a51496d816382c3817c733c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:59:29.743 | INFO     | 95f4d08b1a51496d816382c3817c733c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:59:29.761 | INFO     | 95f4d08b1a51496d816382c3817c733c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:59:29.763 | INFO     | 95f4d08b1a51496d816382c3817c733c | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 28.774ms
2025-08-25 08:59:33.841 | INFO     | 1349f2b17d114b12973c64e8a327648b | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:59:33.843 | INFO     | 1349f2b17d114b12973c64e8a327648b | 成功认证Java用户: pythontest
2025-08-25 08:59:33.849 | INFO     | 1349f2b17d114b12973c64e8a327648b | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:59:33.850 | INFO     | 1349f2b17d114b12973c64e8a327648b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:59:33.864 | INFO     | 1349f2b17d114b12973c64e8a327648b | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:59:33.866 | INFO     | 1349f2b17d114b12973c64e8a327648b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 25.426ms
2025-08-25 08:59:33.888 | INFO     | 47cee50fec224dbd973850a7c1bfd8e6 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 08:59:33.889 | INFO     | 47cee50fec224dbd973850a7c1bfd8e6 | 成功认证Java用户: pythontest
2025-08-25 08:59:33.895 | INFO     | 47cee50fec224dbd973850a7c1bfd8e6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 08:59:33.896 | INFO     | 47cee50fec224dbd973850a7c1bfd8e6 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 08:59:33.916 | INFO     | 47cee50fec224dbd973850a7c1bfd8e6 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 08:59:33.918 | INFO     | 47cee50fec224dbd973850a7c1bfd8e6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 30.873ms
2025-08-25 09:00:57.838 | INFO     | cf23f1b4185e44c0af62e4a91a86676f | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:00:57.839 | INFO     | cf23f1b4185e44c0af62e4a91a86676f | 成功认证Java用户: pythontest
2025-08-25 09:00:57.844 | INFO     | cf23f1b4185e44c0af62e4a91a86676f | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:00:57.844 | INFO     | cf23f1b4185e44c0af62e4a91a86676f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 09:00:57.856 | INFO     | cf23f1b4185e44c0af62e4a91a86676f | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 09:00:57.857 | INFO     | cf23f1b4185e44c0af62e4a91a86676f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 18.902ms
2025-08-25 09:00:57.866 | INFO     | b272d3f20200452f8f870e49cbd3342c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:00:57.867 | INFO     | b272d3f20200452f8f870e49cbd3342c | 成功认证Java用户: pythontest
2025-08-25 09:00:57.871 | INFO     | b272d3f20200452f8f870e49cbd3342c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:00:57.872 | INFO     | b272d3f20200452f8f870e49cbd3342c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 09:00:57.893 | INFO     | b272d3f20200452f8f870e49cbd3342c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 09:00:57.893 | INFO     | b272d3f20200452f8f870e49cbd3342c | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 27.774ms
2025-08-25 09:00:59.449 | INFO     | c8dd56e2f8c94e0c884c16ce4ce5209c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:00:59.450 | INFO     | c8dd56e2f8c94e0c884c16ce4ce5209c | 成功认证Java用户: pythontest
2025-08-25 09:00:59.455 | INFO     | c8dd56e2f8c94e0c884c16ce4ce5209c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:00:59.455 | INFO     | c8dd56e2f8c94e0c884c16ce4ce5209c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 09:00:59.466 | INFO     | c8dd56e2f8c94e0c884c16ce4ce5209c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 09:00:59.467 | INFO     | c8dd56e2f8c94e0c884c16ce4ce5209c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 17.859ms
2025-08-25 09:00:59.479 | INFO     | 49d62cd1510e4f41a2d75710c78826c6 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:00:59.480 | INFO     | 49d62cd1510e4f41a2d75710c78826c6 | 成功认证Java用户: pythontest
2025-08-25 09:00:59.485 | INFO     | 49d62cd1510e4f41a2d75710c78826c6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:00:59.485 | INFO     | 49d62cd1510e4f41a2d75710c78826c6 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 09:00:59.499 | INFO     | 49d62cd1510e4f41a2d75710c78826c6 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 09:00:59.500 | INFO     | 49d62cd1510e4f41a2d75710c78826c6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 21.068ms
2025-08-25 09:06:32.251 | INFO     | 9cba0cbe1cc14f1eb731b7cc92d78eaa | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:06:32.253 | INFO     | 9cba0cbe1cc14f1eb731b7cc92d78eaa | 成功认证Java用户: pythontest
2025-08-25 09:06:32.260 | INFO     | 9cba0cbe1cc14f1eb731b7cc92d78eaa | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:06:32.261 | INFO     | 9cba0cbe1cc14f1eb731b7cc92d78eaa | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 09:06:32.276 | INFO     | 9cba0cbe1cc14f1eb731b7cc92d78eaa | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 09:06:32.277 | INFO     | 9cba0cbe1cc14f1eb731b7cc92d78eaa | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 27.149ms
2025-08-25 09:06:55.988 | INFO     | 97ed8031f44049f0bb4301c96d8181d0 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:06:55.989 | INFO     | 97ed8031f44049f0bb4301c96d8181d0 | 成功认证Java用户: pythontest
2025-08-25 09:06:55.995 | INFO     | 97ed8031f44049f0bb4301c96d8181d0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:06:55.995 | INFO     | 97ed8031f44049f0bb4301c96d8181d0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 09:06:56.010 | INFO     | 97ed8031f44049f0bb4301c96d8181d0 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 09:06:56.012 | INFO     | 97ed8031f44049f0bb4301c96d8181d0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 24.161ms
2025-08-25 09:07:13.366 | INFO     | b3ecfc2429f246a1bb0029dd293ba332 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:07:13.367 | INFO     | b3ecfc2429f246a1bb0029dd293ba332 | 成功认证Java用户: pythontest
2025-08-25 09:07:13.372 | INFO     | b3ecfc2429f246a1bb0029dd293ba332 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:07:13.372 | INFO     | b3ecfc2429f246a1bb0029dd293ba332 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 09:07:13.394 | INFO     | b3ecfc2429f246a1bb0029dd293ba332 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 09:07:13.396 | INFO     | b3ecfc2429f246a1bb0029dd293ba332 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 29.649ms
2025-08-25 09:09:39.400 | INFO     | bba0ccb874c44679bafa4ab496b2460e | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:09:39.400 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:09:39.401 | INFO     | bba0ccb874c44679bafa4ab496b2460e | 成功认证Java用户: pythontest
2025-08-25 09:09:39.401 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | 成功认证Java用户: pythontest
2025-08-25 09:09:39.407 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:09:39.407 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:09:39.409 | INFO     | bba0ccb874c44679bafa4ab496b2460e | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:09:39.410 | INFO     | bba0ccb874c44679bafa4ab496b2460e | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:09:39.434 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/126026f2814c11f0a9aa88f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:09:39.435 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | 文档预览调试 - doc_id: 126026f2814c11f0a9aa88f4da8e1b91
2025-08-25 09:09:39.435 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | doc_name: '新文件 8 (3).txt', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-25 09:09:39.435 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-25 09:09:39.436 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | 文件头检测失败，根据文件名识别类型: text (基于文件名: '新文件 8 (3).txt')
2025-08-25 09:09:39.436 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | 最终识别的文档类型: text
2025-08-25 09:09:39.436 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | 成功使用 utf-8 编码解析文本内容
2025-08-25 09:09:39.436 | INFO     | efab2eb4ace74c1c824270096c6cb0a5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/126026f2814c11f0a9aa88f4da8e1b91/preview/doc_name=%E6%96%B0%E6%96%87%E4%BB%B6+8+(3).txt | 35.748ms
2025-08-25 09:09:39.596 | INFO     | bba0ccb874c44679bafa4ab496b2460e | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/126026f2814c11f0a9aa88f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-25 09:09:39.597 | INFO     | bba0ccb874c44679bafa4ab496b2460e | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/126026f2814c11f0a9aa88f4da8e1b91/chunks/page=1&page_size=50 | 197.026ms
2025-08-25 09:09:45.846 | INFO     | 783155189ba340fb9da56b45e44adbae | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:09:45.847 | INFO     | 783155189ba340fb9da56b45e44adbae | 成功认证Java用户: pythontest
2025-08-25 09:09:45.851 | INFO     | 783155189ba340fb9da56b45e44adbae | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:09:45.853 | INFO     | 783155189ba340fb9da56b45e44adbae | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:09:46.012 | INFO     | 783155189ba340fb9da56b45e44adbae | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/11f978c0814c11f0867288f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-25 09:09:46.014 | INFO     | 783155189ba340fb9da56b45e44adbae | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/11f978c0814c11f0867288f4da8e1b91/chunks/page=1&page_size=50 | 167.799ms
2025-08-25 09:09:46.016 | INFO     | 43d6af553a9549d3bdca4c0df6978953 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:09:46.017 | INFO     | 43d6af553a9549d3bdca4c0df6978953 | 成功认证Java用户: pythontest
2025-08-25 09:09:46.021 | INFO     | 43d6af553a9549d3bdca4c0df6978953 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:09:46.022 | INFO     | 43d6af553a9549d3bdca4c0df6978953 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:09:46.041 | INFO     | 43d6af553a9549d3bdca4c0df6978953 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/11f978c0814c11f0867288f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:09:46.042 | INFO     | 43d6af553a9549d3bdca4c0df6978953 | 文档预览调试 - doc_id: 11f978c0814c11f0867288f4da8e1b91
2025-08-25 09:09:46.042 | INFO     | 43d6af553a9549d3bdca4c0df6978953 | doc_name: 'test(1).pdf', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-25 09:09:46.042 | INFO     | 43d6af553a9549d3bdca4c0df6978953 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-25 09:09:46.043 | INFO     | 43d6af553a9549d3bdca4c0df6978953 | 根据文件头检测到PDF格式，修正类型为pdf
2025-08-25 09:09:46.043 | INFO     | 43d6af553a9549d3bdca4c0df6978953 | 最终识别的文档类型: pdf
2025-08-25 09:09:46.043 | INFO     | 43d6af553a9549d3bdca4c0df6978953 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/11f978c0814c11f0867288f4da8e1b91/preview/doc_name=test(1).pdf | 27.712ms
2025-08-25 09:09:46.052 | INFO     | 0ecbcb27cc2843cabe8781a0df6ba330 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:09:46.053 | INFO     | 0ecbcb27cc2843cabe8781a0df6ba330 | 成功认证Java用户: pythontest
2025-08-25 09:09:46.058 | INFO     | 0ecbcb27cc2843cabe8781a0df6ba330 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:09:46.058 | INFO     | 0ecbcb27cc2843cabe8781a0df6ba330 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:09:46.079 | INFO     | 0ecbcb27cc2843cabe8781a0df6ba330 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/11f978c0814c11f0867288f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:09:46.101 | INFO     | 0ecbcb27cc2843cabe8781a0df6ba330 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/11f978c0814c11f0867288f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:09:46.103 | INFO     | 0ecbcb27cc2843cabe8781a0df6ba330 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/11f978c0814c11f0867288f4da8e1b91/download | 50.706ms
2025-08-25 09:10:45.391 | INFO     | cd4e51b261ce4e45ace5b35889ca960e | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:10:45.391 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:10:45.394 | INFO     | cd4e51b261ce4e45ace5b35889ca960e | 成功认证Java用户: pythontest
2025-08-25 09:10:45.394 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | 成功认证Java用户: pythontest
2025-08-25 09:10:45.400 | INFO     | cd4e51b261ce4e45ace5b35889ca960e | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:10:45.401 | INFO     | cd4e51b261ce4e45ace5b35889ca960e | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:10:45.404 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:10:45.404 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:10:45.484 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:10:45.485 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | 文档预览调试 - doc_id: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 09:10:45.485 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | doc_name: '新文件 8.txt', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-25 09:10:45.486 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-25 09:10:45.486 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | 文件头检测失败，根据文件名识别类型: text (基于文件名: '新文件 8.txt')
2025-08-25 09:10:45.486 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | 最终识别的文档类型: text
2025-08-25 09:10:45.486 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | 成功使用 utf-8 编码解析文本内容
2025-08-25 09:10:45.486 | INFO     | 4aa9c429597f40c3b14c38e9ed1b9b8d | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/preview/doc_name=%E6%96%B0%E6%96%87%E4%BB%B6+8.txt | 95.009ms
2025-08-25 09:10:45.580 | INFO     | cd4e51b261ce4e45ace5b35889ca960e | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-25 09:10:45.581 | INFO     | cd4e51b261ce4e45ace5b35889ca960e | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/chunks/page=1&page_size=50 | 190.349ms
2025-08-25 09:10:52.048 | INFO     | 0dd7cf3f618c4d4488973b96c0d9c49f | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:10:52.049 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:10:52.050 | INFO     | 0dd7cf3f618c4d4488973b96c0d9c49f | 成功认证Java用户: pythontest
2025-08-25 09:10:52.050 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | 成功认证Java用户: pythontest
2025-08-25 09:10:52.054 | INFO     | 0dd7cf3f618c4d4488973b96c0d9c49f | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:10:52.055 | INFO     | 0dd7cf3f618c4d4488973b96c0d9c49f | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:10:52.057 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:10:52.057 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:10:52.079 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fb903ac27e3311f0a32a88f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:10:52.079 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | 文档预览调试 - doc_id: fb903ac27e3311f0a32a88f4da8e1b91
2025-08-25 09:10:52.079 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | doc_name: 'test.docx', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-25 09:10:52.079 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-25 09:10:52.080 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | 根据文件头检测到Office文档格式(ZIP)，修正类型为office
2025-08-25 09:10:52.080 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | 最终识别的文档类型: office
2025-08-25 09:10:52.080 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | 检测到Office文档: test.docx，返回原始文档URL用于vue-office预览
2025-08-25 09:10:52.080 | INFO     | 2c63d2ba9208430cbdde319e0f532ed3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fb903ac27e3311f0a32a88f4da8e1b91/preview/doc_name=test.docx | 31.900ms
2025-08-25 09:10:52.086 | INFO     | c86e9bb97f8e495093e3fae9c03951e2 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:10:52.087 | INFO     | c86e9bb97f8e495093e3fae9c03951e2 | 成功认证Java用户: pythontest
2025-08-25 09:10:52.091 | INFO     | c86e9bb97f8e495093e3fae9c03951e2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:10:52.091 | INFO     | c86e9bb97f8e495093e3fae9c03951e2 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:10:52.112 | INFO     | c86e9bb97f8e495093e3fae9c03951e2 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fb903ac27e3311f0a32a88f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:10:52.127 | INFO     | c86e9bb97f8e495093e3fae9c03951e2 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fb903ac27e3311f0a32a88f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:10:52.128 | INFO     | c86e9bb97f8e495093e3fae9c03951e2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fb903ac27e3311f0a32a88f4da8e1b91/content | 42.367ms
2025-08-25 09:10:52.178 | INFO     | 0dd7cf3f618c4d4488973b96c0d9c49f | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fb903ac27e3311f0a32a88f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-25 09:10:52.179 | INFO     | 0dd7cf3f618c4d4488973b96c0d9c49f | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fb903ac27e3311f0a32a88f4da8e1b91/chunks/page=1&page_size=50 | 131.241ms
2025-08-25 09:11:26.843 | INFO     | 0217c040a3fe4c0eb0a1ce42e89fabad | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:11:26.844 | INFO     | 0217c040a3fe4c0eb0a1ce42e89fabad | 成功认证Java用户: pythontest
2025-08-25 09:11:26.849 | INFO     | 0217c040a3fe4c0eb0a1ce42e89fabad | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:11:26.849 | INFO     | 0217c040a3fe4c0eb0a1ce42e89fabad | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 09:11:26.865 | INFO     | 0217c040a3fe4c0eb0a1ce42e89fabad | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 09:11:26.867 | INFO     | 0217c040a3fe4c0eb0a1ce42e89fabad | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 23.894ms
2025-08-25 09:11:26.877 | INFO     | 7b25de273d0148ada2da47dc1886a039 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:11:26.878 | INFO     | 7b25de273d0148ada2da47dc1886a039 | 成功认证Java用户: pythontest
2025-08-25 09:11:26.883 | INFO     | 7b25de273d0148ada2da47dc1886a039 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:11:26.883 | INFO     | 7b25de273d0148ada2da47dc1886a039 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 09:11:26.896 | INFO     | 7b25de273d0148ada2da47dc1886a039 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 09:11:26.897 | INFO     | 7b25de273d0148ada2da47dc1886a039 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=20&orderby=create_time&desc=true | 20.143ms
2025-08-25 09:11:33.063 | INFO     | 304a00cf49464a61ac5b26b9463ab3aa | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:11:33.064 | INFO     | 304a00cf49464a61ac5b26b9463ab3aa | 成功认证Java用户: pythontest
2025-08-25 09:11:33.112 | INFO     | 304a00cf49464a61ac5b26b9463ab3aa | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:11:33.112 | INFO     | 304a00cf49464a61ac5b26b9463ab3aa | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-25 09:11:33.244 | INFO     | 304a00cf49464a61ac5b26b9463ab3aa | 文件上传完成: test (1).pptx, 大小: 11122759 bytes
2025-08-25 09:11:33.429 | INFO     | 304a00cf49464a61ac5b26b9463ab3aa | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-25 09:11:33.431 | INFO     | 304a00cf49464a61ac5b26b9463ab3aa | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 367.611ms
2025-08-25 09:11:50.505 | INFO     | 57517fe0f4c84136b920800085087746 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:11:50.506 | INFO     | 57517fe0f4c84136b920800085087746 | 成功认证Java用户: pythontest
2025-08-25 09:11:50.556 | INFO     | 57517fe0f4c84136b920800085087746 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:11:50.556 | INFO     | 57517fe0f4c84136b920800085087746 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-25 09:11:50.698 | INFO     | 57517fe0f4c84136b920800085087746 | 文件上传完成: test (1).pptx, 大小: 11122759 bytes
2025-08-25 09:11:50.899 | INFO     | 57517fe0f4c84136b920800085087746 | HTTP Request: POST http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents "HTTP/1.1 200 OK"
2025-08-25 09:11:50.901 | INFO     | 57517fe0f4c84136b920800085087746 | 127.0.0.1       | POST     | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/upload | 397.067ms
2025-08-25 09:12:11.019 | INFO     | b6963594ab72496a84affa009635af71 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:12:11.020 | INFO     | b6963594ab72496a84affa009635af71 | 成功认证Java用户: pythontest
2025-08-25 09:12:11.023 | INFO     | b6963594ab72496a84affa009635af71 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:12:11.024 | INFO     | b6963594ab72496a84affa009635af71 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 09:12:11.045 | INFO     | b6963594ab72496a84affa009635af71 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 09:12:11.046 | INFO     | b6963594ab72496a84affa009635af71 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 27.646ms
2025-08-25 09:12:11.060 | INFO     | 25f54d40810e43a4a4deb000ce1551ca | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:12:11.061 | INFO     | 25f54d40810e43a4a4deb000ce1551ca | 成功认证Java用户: pythontest
2025-08-25 09:12:11.064 | INFO     | 25f54d40810e43a4a4deb000ce1551ca | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:12:11.065 | INFO     | 25f54d40810e43a4a4deb000ce1551ca | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-25 09:12:11.077 | INFO     | 25f54d40810e43a4a4deb000ce1551ca | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-25 09:12:11.078 | INFO     | 25f54d40810e43a4a4deb000ce1551ca | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/list/page=1&page_size=100 | 17.817ms
2025-08-25 09:13:46.165 | INFO     | 12607f4d1efa45849a124ac4cf8c7761 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:13:46.166 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:13:46.167 | INFO     | 12607f4d1efa45849a124ac4cf8c7761 | 成功认证Java用户: pythontest
2025-08-25 09:13:46.168 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | 成功认证Java用户: pythontest
2025-08-25 09:13:46.173 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:13:46.174 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:13:46.176 | INFO     | 12607f4d1efa45849a124ac4cf8c7761 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:13:46.176 | INFO     | 12607f4d1efa45849a124ac4cf8c7761 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:13:46.220 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/126026f2814c11f0a9aa88f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:13:46.220 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | 文档预览调试 - doc_id: 126026f2814c11f0a9aa88f4da8e1b91
2025-08-25 09:13:46.220 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | doc_name: '新文件 8 (3).txt', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-25 09:13:46.220 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-25 09:13:46.221 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | 文件头检测失败，根据文件名识别类型: text (基于文件名: '新文件 8 (3).txt')
2025-08-25 09:13:46.221 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | 最终识别的文档类型: text
2025-08-25 09:13:46.221 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | 成功使用 utf-8 编码解析文本内容
2025-08-25 09:13:46.221 | INFO     | 718dc36cadc948498dda7fb86a0c1523 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/126026f2814c11f0a9aa88f4da8e1b91/preview/doc_name=%E6%96%B0%E6%96%87%E4%BB%B6+8+(3).txt | 55.270ms
2025-08-25 09:13:46.286 | INFO     | 12607f4d1efa45849a124ac4cf8c7761 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/126026f2814c11f0a9aa88f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-25 09:13:46.287 | INFO     | 12607f4d1efa45849a124ac4cf8c7761 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/126026f2814c11f0a9aa88f4da8e1b91/chunks/page=1&page_size=50 | 121.498ms
2025-08-25 09:13:50.303 | INFO     | 87031cf3895643e5a7e805fec77c5865 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:13:50.304 | INFO     | b1853150078848b392e16d01ae48616c | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:13:50.305 | INFO     | 87031cf3895643e5a7e805fec77c5865 | 成功认证Java用户: pythontest
2025-08-25 09:13:50.305 | INFO     | b1853150078848b392e16d01ae48616c | 成功认证Java用户: pythontest
2025-08-25 09:13:50.309 | INFO     | 87031cf3895643e5a7e805fec77c5865 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:13:50.309 | INFO     | 87031cf3895643e5a7e805fec77c5865 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:13:50.312 | INFO     | b1853150078848b392e16d01ae48616c | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:13:50.312 | INFO     | b1853150078848b392e16d01ae48616c | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:13:50.341 | INFO     | b1853150078848b392e16d01ae48616c | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/11f978c0814c11f0867288f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:13:50.343 | INFO     | b1853150078848b392e16d01ae48616c | 文档预览调试 - doc_id: 11f978c0814c11f0867288f4da8e1b91
2025-08-25 09:13:50.343 | INFO     | b1853150078848b392e16d01ae48616c | doc_name: 'test(1).pdf', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-25 09:13:50.343 | INFO     | b1853150078848b392e16d01ae48616c | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-25 09:13:50.343 | INFO     | b1853150078848b392e16d01ae48616c | 根据文件头检测到PDF格式，修正类型为pdf
2025-08-25 09:13:50.344 | INFO     | b1853150078848b392e16d01ae48616c | 最终识别的文档类型: pdf
2025-08-25 09:13:50.344 | INFO     | b1853150078848b392e16d01ae48616c | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/11f978c0814c11f0867288f4da8e1b91/preview/doc_name=test(1).pdf | 40.122ms
2025-08-25 09:13:50.349 | INFO     | 6627844a84e041289e553bf982b90a42 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:13:50.350 | INFO     | 6627844a84e041289e553bf982b90a42 | 成功认证Java用户: pythontest
2025-08-25 09:13:50.354 | INFO     | 6627844a84e041289e553bf982b90a42 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:13:50.354 | INFO     | 6627844a84e041289e553bf982b90a42 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:13:50.371 | INFO     | 6627844a84e041289e553bf982b90a42 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/11f978c0814c11f0867288f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:13:50.388 | INFO     | 6627844a84e041289e553bf982b90a42 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/11f978c0814c11f0867288f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:13:50.389 | INFO     | 6627844a84e041289e553bf982b90a42 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/11f978c0814c11f0867288f4da8e1b91/download | 41.416ms
2025-08-25 09:13:50.437 | INFO     | 87031cf3895643e5a7e805fec77c5865 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/11f978c0814c11f0867288f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-25 09:13:50.439 | INFO     | 87031cf3895643e5a7e805fec77c5865 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/11f978c0814c11f0867288f4da8e1b91/chunks/page=1&page_size=50 | 135.517ms
2025-08-25 09:14:01.396 | INFO     | 9e61cc94f0e74105a32f7fdc1051e5c8 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:14:01.397 | INFO     | 9e61cc94f0e74105a32f7fdc1051e5c8 | 成功认证Java用户: pythontest
2025-08-25 09:14:01.401 | INFO     | 9e61cc94f0e74105a32f7fdc1051e5c8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:14:01.401 | INFO     | 9e61cc94f0e74105a32f7fdc1051e5c8 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:14:01.510 | INFO     | 9e61cc94f0e74105a32f7fdc1051e5c8 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91/chunks?page=1&page_size=50 "HTTP/1.1 200 OK"
2025-08-25 09:14:01.512 | INFO     | 9e61cc94f0e74105a32f7fdc1051e5c8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/chunks/page=1&page_size=50 | 115.318ms
2025-08-25 09:14:01.513 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | JWT标准验证成功，获取UUID: 0b0cbfa8-d3e5-44fd-a928-53ec6c677174
2025-08-25 09:14:01.515 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | 成功认证Java用户: pythontest
2025-08-25 09:14:01.520 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=22
2025-08-25 09:14:01.522 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-25 09:14:01.549 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets/f1d907257e3311f0954288f4da8e1b91/documents/fbeb1a207e3311f0920888f4da8e1b91 "HTTP/1.1 200 OK"
2025-08-25 09:14:01.550 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | 文档预览调试 - doc_id: fbeb1a207e3311f0920888f4da8e1b91
2025-08-25 09:14:01.550 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | doc_name: '新文件 8.txt', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-25 09:14:01.550 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-25 09:14:01.550 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | 文件头检测失败，根据文件名识别类型: text (基于文件名: '新文件 8.txt')
2025-08-25 09:14:01.551 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | 最终识别的文档类型: text
2025-08-25 09:14:01.551 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | 成功使用 utf-8 编码解析文本内容
2025-08-25 09:14:01.551 | INFO     | d1a4b8ddcf1644fbaa4b99f2f260c5e3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/f1d907257e3311f0954288f4da8e1b91/fbeb1a207e3311f0920888f4da8e1b91/preview/doc_name=%E6%96%B0%E6%96%87%E4%BB%B6+8.txt | 37.755ms
