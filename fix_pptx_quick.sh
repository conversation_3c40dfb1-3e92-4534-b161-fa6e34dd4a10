#!/bin/bash

# 快速修复PPTX上传问题脚本
# 使用方法: bash fix_pptx_quick.sh

echo "🔧 开始修复PPTX文件上传问题..."

# 检测部署方式
if command -v docker &> /dev/null && docker ps | grep -q ragflow; then
    echo "📦 检测到Docker部署"
    DEPLOYMENT="docker"
    CONTAINER_NAME=$(docker ps | grep ragflow | awk '{print $NF}' | head -1)
    echo "容器名称: $CONTAINER_NAME"
elif pgrep -f ragflow &> /dev/null; then
    echo "🐍 检测到直接Python部署"
    DEPLOYMENT="python"
else
    echo "❌ 未检测到运行中的RAGFlow服务"
    echo "请确保RAGFlow正在运行"
    exit 1
fi

# 安装aspose库
echo "📥 安装aspose-slides-python库..."

if [ "$DEPLOYMENT" = "docker" ]; then
    # Docker部署
    echo "在Docker容器中安装..."
    docker exec -it $CONTAINER_NAME pip install aspose-slides-python
    
    if [ $? -eq 0 ]; then
        echo "✅ 安装成功"
        
        # 验证安装
        echo "🔍 验证安装..."
        docker exec -it $CONTAINER_NAME python -c "import aspose.slides; print('Aspose安装验证成功')"
        
        if [ $? -eq 0 ]; then
            echo "✅ 验证成功"
            
            # 重启容器
            echo "🔄 重启RAGFlow容器..."
            docker restart $CONTAINER_NAME
            
            echo "✅ 修复完成！请等待容器重启后重试上传PPTX文件"
        else
            echo "❌ 验证失败，请检查安装"
        fi
    else
        echo "❌ 安装失败"
        echo "尝试其他安装方式..."
        
        # 尝试其他包名
        docker exec -it $CONTAINER_NAME pip install aspose.slides
        docker exec -it $CONTAINER_NAME pip install aspose-slides
    fi
    
elif [ "$DEPLOYMENT" = "python" ]; then
    # 直接Python部署
    echo "在Python环境中安装..."
    
    # 尝试找到RAGFlow的Python环境
    if [ -f "/opt/ragflow/venv/bin/activate" ]; then
        source /opt/ragflow/venv/bin/activate
    elif [ -f "./venv/bin/activate" ]; then
        source ./venv/bin/activate
    fi
    
    pip install aspose-slides-python
    
    if [ $? -eq 0 ]; then
        echo "✅ 安装成功"
        
        # 验证安装
        echo "🔍 验证安装..."
        python -c "import aspose.slides; print('Aspose安装验证成功')"
        
        if [ $? -eq 0 ]; then
            echo "✅ 验证成功"
            
            # 重启服务
            echo "🔄 重启RAGFlow服务..."
            if command -v systemctl &> /dev/null; then
                sudo systemctl restart ragflow
            elif command -v supervisorctl &> /dev/null; then
                supervisorctl restart ragflow
            else
                echo "⚠️  请手动重启RAGFlow服务"
            fi
            
            echo "✅ 修复完成！请重试上传PPTX文件"
        else
            echo "❌ 验证失败，请检查安装"
        fi
    else
        echo "❌ 安装失败，请检查Python环境和权限"
    fi
fi

echo ""
echo "📋 如果问题仍然存在，请："
echo "1. 检查RAGFlow日志"
echo "2. 确认Python环境正确"
echo "3. 尝试手动安装: pip install aspose-slides-python"
echo "4. 重启RAGFlow服务"
